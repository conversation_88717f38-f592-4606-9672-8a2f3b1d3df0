# 🐳 杏仁解析项目 Docker 部署完整指南

## 📋 项目概述

本项目实现了完全解耦的Docker部署方案，支持以下服务的独立或组合部署：

- **infrastructure** - 基础设施（MySQL + Redis + Nginx）
- **almond-parser** - 杏仁解析服务（任务调度中心）
- **mineru-api** - MinerU解析引擎（解析节点）
- **web** - Web管理后台（Dashboard）

## 🏗️ 架构特点

### ✅ 完全解耦设计
- 每个服务都可以独立部署和扩展
- 支持分布式部署（不同服务器）
- 灵活的组合部署模式

### ✅ 跨平台支持
- **Linux/macOS**: 使用 `.sh` 脚本
- **Windows**: 使用 `.bat` 批处理脚本
- 统一的Docker Compose配置

### ✅ GPU智能适配
- 自动检测GPU支持
- GPU/CPU模式自动切换
- 混合节点部署策略

## 🚀 快速开始

### Windows用户
```batch
# 1. 构建镜像
scripts\build.bat

# 2. 完整部署
scripts\deploy-full.bat

# 3. 访问系统
# Web管理平台: http://localhost
# API文档: http://localhost:8000/docs
```

### Linux/macOS用户
```bash
# 1. 构建镜像
./scripts/build.sh

# 2. 完整部署
./scripts/deploy-full.sh

# 3. 访问系统
# Web管理平台: http://localhost
# API文档: http://localhost:8000/docs
```

## 📦 部署模式详解

### 🎯 独立服务部署

#### 1. 基础设施独立部署
```bash
# Linux/macOS
./scripts/deploy-infrastructure.sh

# Windows
scripts\deploy-infrastructure.bat
```
**包含服务**: MySQL + Redis + Nginx  
**适用场景**: 作为共享基础设施

#### 2. 杏仁解析服务独立部署
```bash
# Linux/macOS
./scripts/deploy-almond-parser.sh

# Windows
scripts\deploy-almond-parser.bat
```
**包含服务**: 杏仁解析API + ARQ Worker  
**适用场景**: 任务调度中心

#### 3. MinerU解析引擎独立部署
```bash
# Linux/macOS
./scripts/deploy-mineru-api.sh

# Windows
scripts\deploy-mineru-api.bat
```
**包含服务**: MinerU API + LibreOffice  
**适用场景**: 解析节点

#### 4. Web前端独立部署
```bash
# Linux/macOS
./scripts/deploy-web.sh

# Windows
scripts\deploy-web.bat
```
**包含服务**: Web前端  
**适用场景**: 管理界面

### 🎯 组合部署模式

#### 1. 管理平台部署（推荐）
```bash
# Linux/macOS
./scripts/deploy-management.sh

# Windows
scripts\deploy-management.bat
```
**包含服务**: 基础设施 + 杏仁解析 + Web前端  
**适用场景**: 管理中心，无GPU解析

#### 2. 完整部署
```bash
# Linux/macOS
./scripts/deploy-full.sh

# Windows
scripts\deploy-full.bat
```
**包含服务**: 所有服务  
**适用场景**: 单机全功能部署

#### 3. 解析集群扩展
```bash
# Linux/macOS
./scripts/scale-mineru.sh 3

# Windows
scripts\scale-mineru.bat 3
```
**功能**: 扩展到指定数量的解析节点  
**适用场景**: 高并发解析需求

## 🔧 配置文件说明

### 环境变量配置
```
config/
├── .env.infrastructure    # 基础设施配置
├── .env.almond-parser     # 杏仁解析配置
├── .env.mineru-api        # 解析引擎配置
└── .env.web               # Web前端配置
```

### 关键配置项

#### 基础设施配置 (.env.infrastructure)
```env
MYSQL_ROOT_PASSWORD=parserflow123
MYSQL_DATABASE=parserflow
MYSQL_USER=parserflow
MYSQL_PASSWORD=parserflow123
NGINX_PORT=80
```

#### 杏仁解析配置 (.env.almond-parser)
```env
SECRET_KEY=your-secret-key-change-this-in-production
MAX_CONCURRENT_TASKS=10
TASK_TIMEOUT=300
LOG_LEVEL=INFO
```

#### MinerU配置 (.env.mineru-api)
```env
CUDA_VISIBLE_DEVICES=0
GPU_COUNT=1
ENABLE_DOC_CONVERSION=true
MAX_CONCURRENT_REQUESTS=5
```

## 🌐 网络架构

### 端口映射
| 服务 | 内部端口 | 外部端口 | 说明 |
|------|---------|---------|------|
| Nginx | 80 | 80 | Web入口 |
| Almond Parser | 8000 | 8000 | API服务 |
| MinerU API | 8001 | 8001+ | 解析服务 |
| MySQL | 3306 | 3306 | 数据库 |
| Redis | 6379 | 6379 | 缓存 |

### 服务通信
- 所有服务使用 `parserflow-network` 网络
- 服务间通过服务名访问
- 支持跨主机部署（overlay网络）

## 💾 数据持久化

### 数据目录结构
```
data/
├── mysql/          # MySQL数据
├── redis/          # Redis数据
├── uploads/        # 上传文件
├── output/         # 输出文件
└── mineru/         # MinerU数据
    ├── temp/       # 临时文件
    ├── output/     # 解析输出
    ├── models/     # AI模型
    └── config/     # 配置文件

logs/
├── mysql/          # MySQL日志
├── redis/          # Redis日志
├── almond/         # 杏仁解析日志
├── mineru/         # MinerU日志
└── nginx/          # Nginx日志
```

## 🔍 故障排除

### 常见问题及解决方案

#### 1. GPU不可用
```bash
# 检查GPU支持
nvidia-smi
docker run --gpus all nvidia/cuda:11.8-base nvidia-smi
```

#### 2. 端口冲突
```bash
# Windows
netstat -an | findstr :8000

# Linux/macOS
netstat -tulpn | grep :8000
```

#### 3. 服务启动失败
```bash
# 查看日志
docker-compose -f compose/full.yml logs -f

# 查看特定服务日志
docker-compose -f compose/full.yml logs almond-parser
```

#### 4. 网络连接问题
```bash
# 检查网络
docker network ls
docker network inspect parserflow-network
```

### 日志查看命令
```bash
# 查看所有服务日志
docker-compose -f compose/full.yml logs -f

# 查看特定服务日志
docker logs parserflow-almond-parser -f
docker logs parserflow-mineru-api -f
docker logs parserflow-web -f
```

## 🚨 生产环境建议

### 安全配置
1. **修改默认密码**
   - 数据库密码
   - Redis密码（如需要）
   - API密钥

2. **启用HTTPS**
   - 配置SSL证书
   - 更新Nginx配置

3. **网络安全**
   - 限制端口访问
   - 配置防火墙规则

### 性能优化
1. **资源分配**
   - 根据负载调整容器资源限制
   - 优化数据库配置

2. **存储优化**
   - 使用SSD存储
   - 定期清理临时文件

3. **监控告警**
   - 配置健康检查
   - 设置资源监控

## 📈 扩展部署

### 分布式部署示例

#### 服务器A：管理平台
```bash
./scripts/deploy-management.sh
```

#### 服务器B：GPU解析节点
```bash
./scripts/deploy-mineru-api.sh
```

#### 服务器C：CPU解析节点
```bash
CUDA_VISIBLE_DEVICES="" ./scripts/deploy-mineru-api.sh
```

### 负载均衡配置
- 使用Nginx upstream配置
- 支持多个MinerU API节点
- 自动故障转移

## 🎯 最佳实践

### 开发环境
- 使用 `deploy-full.sh` 单机部署
- 启用调试模式
- 使用开发配置

### 测试环境
- 使用 `deploy-management.sh` 无GPU部署
- 使用内存数据库加速测试
- 启用详细日志

### 生产环境
- 分离数据库到独立服务器
- 使用 `deploy-management.sh` + 多个 `deploy-mineru-api.sh`
- 配置监控和备份

---

**🌰 杏仁解析 - 智能文档解析的容器化部署方案**

*完全解耦 | 灵活扩展 | 跨平台支持*
