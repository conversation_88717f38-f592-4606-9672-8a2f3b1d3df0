version: '3.8'

services:
  # 杏仁解析服务
  almond-parser:
    image: almond-parser:latest
    container_name: parserflow-almond-parser
    restart: unless-stopped
    environment:
      # 数据库配置
      DATABASE_URL: mysql+aiomysql://${MYSQL_USER:-parserflow}:${MYSQL_PASSWORD:-parserflow123}@mysql:3306/${MYSQL_DATABASE:-parserflow}
      
      # Redis配置
      REDIS_URL: redis://redis:6379/0
      
      # 服务配置
      HOST: 0.0.0.0
      PORT: 8000
      DEBUG: ${DEBUG:-false}
      
      # 认证配置
      SECRET_KEY: ${SECRET_KEY:-your-secret-key-here}
      ACCESS_TOKEN_EXPIRE_MINUTES: ${ACCESS_TOKEN_EXPIRE_MINUTES:-30}
      
      # 文件上传配置
      UPLOAD_DIR: /app/uploads
      MAX_FILE_SIZE: ${MAX_FILE_SIZE:-100MB}
      
      # 日志配置
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
      LOG_DIR: /app/logs
      
      # 任务配置
      MAX_CONCURRENT_TASKS: ${MAX_CONCURRENT_TASKS:-10}
      TASK_TIMEOUT: ${TASK_TIMEOUT:-300}
      
      # 节点管理配置
      NODE_HEALTH_CHECK_INTERVAL: ${NODE_HEALTH_CHECK_INTERVAL:-30}
      NODE_RETRY_INTERVAL: ${NODE_RETRY_INTERVAL:-60}
      
    ports:
      - "${ALMOND_PARSER_PORT:-8000}:8000"
    volumes:
      - uploads_data:/app/uploads
      - almond_logs:/app/logs
      - almond_output:/app/output
    networks:
      - parserflow-network
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # ARQ Worker (异步任务处理)
  almond-worker:
    image: almond-parser:latest
    container_name: parserflow-almond-worker
    restart: unless-stopped
    environment:
      # 继承主服务的环境变量
      DATABASE_URL: mysql+aiomysql://${MYSQL_USER:-parserflow}:${MYSQL_PASSWORD:-parserflow123}@mysql:3306/${MYSQL_DATABASE:-parserflow}
      REDIS_URL: redis://redis:6379/0
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
      LOG_DIR: /app/logs
    volumes:
      - uploads_data:/app/uploads
      - almond_logs:/app/logs
      - almond_output:/app/output
    networks:
      - parserflow-network
    depends_on:
      - almond-parser
    command: ["uv", "run", "python", "worker.py"]
    healthcheck:
      test: ["CMD", "ps", "aux", "|", "grep", "worker.py"]
      interval: 60s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  uploads_data:
    driver: local
  almond_logs:
    driver: local
  almond_output:
    driver: local

networks:
  parserflow-network:
    external: true
