version: '3.8'

services:
  # MinerU API 解析引擎 - 节点1
  mineru-api-1:
    image: mineru-api:latest
    container_name: parserflow-mineru-api-1
    restart: unless-stopped
    environment:
      HOST: 0.0.0.0
      PORT: 8001
      DEBUG: ${DEBUG:-false}
      CUDA_VISIBLE_DEVICES: "0"
      NVIDIA_VISIBLE_DEVICES: "0"
      MINERU_CONFIG_PATH: /app/config/mineru_config.json
      VLM_MODEL_PATH: ${VLM_MODEL_PATH:-/app/models}
      VLM_SERVER_URL: ${VLM_SERVER_URL_1:-http://127.0.0.1:30000}
      PIPELINE_MODE: ${PIPELINE_MODE:-auto}
      LIBREOFFICE_PATH: /usr/bin/libreoffice
      ENABLE_DOC_CONVERSION: ${ENABLE_DOC_CONVERSION:-true}
      TEMP_DIR: /app/temp
      OUTPUT_DIR: /app/output
      MAX_FILE_SIZE: ${MAX_FILE_SIZE:-100MB}
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
      LOG_DIR: /app/logs
      MAX_CONCURRENT_REQUESTS: ${MAX_CONCURRENT_REQUESTS:-5}
      REQUEST_TIMEOUT: ${REQUEST_TIMEOUT:-300}
      API_KEY_REQUIRED: ${API_KEY_REQUIRED:-false}
      API_KEY: ${API_KEY:-}
      NODE_ID: mineru-api-1
    ports:
      - "${MINERU_API_PORT_1:-8001}:8001"
    volumes:
      - mineru_temp_1:/app/temp
      - mineru_output_1:/app/output
      - mineru_logs_1:/app/logs
      - mineru_models:/app/models:ro
      - mineru_config:/app/config:ro
    networks:
      - parserflow-network
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              device_ids: ["0"]
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

  # MinerU API 解析引擎 - 节点2
  mineru-api-2:
    image: mineru-api:latest
    container_name: parserflow-mineru-api-2
    restart: unless-stopped
    environment:
      HOST: 0.0.0.0
      PORT: 8001
      DEBUG: ${DEBUG:-false}
      CUDA_VISIBLE_DEVICES: "1"
      NVIDIA_VISIBLE_DEVICES: "1"
      MINERU_CONFIG_PATH: /app/config/mineru_config.json
      VLM_MODEL_PATH: ${VLM_MODEL_PATH:-/app/models}
      VLM_SERVER_URL: ${VLM_SERVER_URL_2:-http://127.0.0.1:30001}
      PIPELINE_MODE: ${PIPELINE_MODE:-auto}
      LIBREOFFICE_PATH: /usr/bin/libreoffice
      ENABLE_DOC_CONVERSION: ${ENABLE_DOC_CONVERSION:-true}
      TEMP_DIR: /app/temp
      OUTPUT_DIR: /app/output
      MAX_FILE_SIZE: ${MAX_FILE_SIZE:-100MB}
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
      LOG_DIR: /app/logs
      MAX_CONCURRENT_REQUESTS: ${MAX_CONCURRENT_REQUESTS:-5}
      REQUEST_TIMEOUT: ${REQUEST_TIMEOUT:-300}
      API_KEY_REQUIRED: ${API_KEY_REQUIRED:-false}
      API_KEY: ${API_KEY:-}
      NODE_ID: mineru-api-2
    ports:
      - "${MINERU_API_PORT_2:-8002}:8001"
    volumes:
      - mineru_temp_2:/app/temp
      - mineru_output_2:/app/output
      - mineru_logs_2:/app/logs
      - mineru_models:/app/models:ro
      - mineru_config:/app/config:ro
    networks:
      - parserflow-network
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              device_ids: ["1"]
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

  # MinerU API 解析引擎 - 节点3 (CPU节点)
  mineru-api-3:
    image: mineru-api:latest
    container_name: parserflow-mineru-api-3
    restart: unless-stopped
    environment:
      HOST: 0.0.0.0
      PORT: 8001
      DEBUG: ${DEBUG:-false}
      # CPU模式，不设置GPU
      MINERU_CONFIG_PATH: /app/config/mineru_config.json
      PIPELINE_MODE: pipeline
      LIBREOFFICE_PATH: /usr/bin/libreoffice
      ENABLE_DOC_CONVERSION: ${ENABLE_DOC_CONVERSION:-true}
      TEMP_DIR: /app/temp
      OUTPUT_DIR: /app/output
      MAX_FILE_SIZE: ${MAX_FILE_SIZE:-100MB}
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
      LOG_DIR: /app/logs
      MAX_CONCURRENT_REQUESTS: ${MAX_CONCURRENT_REQUESTS:-10}
      REQUEST_TIMEOUT: ${REQUEST_TIMEOUT:-300}
      API_KEY_REQUIRED: ${API_KEY_REQUIRED:-false}
      API_KEY: ${API_KEY:-}
      NODE_ID: mineru-api-3
      NODE_TYPE: pipeline
    ports:
      - "${MINERU_API_PORT_3:-8003}:8001"
    volumes:
      - mineru_temp_3:/app/temp
      - mineru_output_3:/app/output
      - mineru_logs_3:/app/logs
      - mineru_config:/app/config:ro
    networks:
      - parserflow-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

volumes:
  mineru_temp_1:
    driver: local
  mineru_output_1:
    driver: local
  mineru_logs_1:
    driver: local
  mineru_temp_2:
    driver: local
  mineru_output_2:
    driver: local
  mineru_logs_2:
    driver: local
  mineru_temp_3:
    driver: local
  mineru_output_3:
    driver: local
  mineru_logs_3:
    driver: local
  mineru_models:
    driver: local
  mineru_config:
    driver: local

networks:
  parserflow-network:
    external: true
