version: '3.8'

services:
  # MinerU API 解析引擎
  mineru-api:
    image: mineru-api:latest
    container_name: parserflow-mineru-api
    restart: unless-stopped
    environment:
      # 服务配置
      HOST: 0.0.0.0
      PORT: 8001
      DEBUG: ${DEBUG:-false}
      
      # GPU配置
      CUDA_VISIBLE_DEVICES: ${CUDA_VISIBLE_DEVICES:-0}
      NVIDIA_VISIBLE_DEVICES: ${NVIDIA_VISIBLE_DEVICES:-all}
      
      # MinerU配置
      MINERU_CONFIG_PATH: /app/config/mineru_config.json
      
      # VLM配置
      VLM_MODEL_PATH: ${VLM_MODEL_PATH:-/app/models}
      VLM_SERVER_URL: ${VLM_SERVER_URL:-http://127.0.0.1:30000}
      
      # Pipeline配置
      PIPELINE_MODE: ${PIPELINE_MODE:-auto}
      
      # LibreOffice配置
      LIBREOFFICE_PATH: /usr/bin/libreoffice
      ENABLE_DOC_CONVERSION: ${ENABLE_DOC_CONVERSION:-true}
      
      # 文件处理配置
      TEMP_DIR: /app/temp
      OUTPUT_DIR: /app/output
      MAX_FILE_SIZE: ${MAX_FILE_SIZE:-100MB}
      
      # 日志配置
      LOG_LEVEL: ${LOG_LEVEL:-INFO}
      LOG_DIR: /app/logs
      
      # 性能配置
      MAX_CONCURRENT_REQUESTS: ${MAX_CONCURRENT_REQUESTS:-5}
      REQUEST_TIMEOUT: ${REQUEST_TIMEOUT:-300}
      
      # 认证配置（如果启用）
      API_KEY_REQUIRED: ${API_KEY_REQUIRED:-false}
      API_KEY: ${API_KEY:-}
      
    ports:
      - "${MINERU_API_PORT:-8001}:8001"
    volumes:
      - mineru_temp:/app/temp
      - mineru_output:/app/output
      - mineru_logs:/app/logs
      - mineru_models:/app/models
      - mineru_config:/app/config
    networks:
      - parserflow-network
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: ${GPU_COUNT:-1}
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

volumes:
  mineru_temp:
    driver: local
  mineru_output:
    driver: local
  mineru_logs:
    driver: local
  mineru_models:
    driver: local
  mineru_config:
    driver: local

networks:
  parserflow-network:
    external: true
