#!/bin/bash

# 构建所有Docker镜像的脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未启动，请启动 Docker 服务"
        exit 1
    fi
    
    log_success "Docker 环境检查通过"
}

# 构建镜像函数
build_image() {
    local dockerfile=$1
    local image_name=$2
    local context_path=$3
    local platform=${4:-"linux"}
    
    log_info "构建镜像: $image_name"
    log_info "Dockerfile: $dockerfile"
    log_info "Context: $context_path"
    
    if [ "$platform" = "gpu" ]; then
        docker build \
            --build-arg PLATFORM=linux \
            -f "$dockerfile" \
            -t "$image_name" \
            "$context_path"
    else
        docker build \
            -f "$dockerfile" \
            -t "$image_name" \
            "$context_path"
    fi
    
    if [ $? -eq 0 ]; then
        log_success "镜像构建成功: $image_name"
    else
        log_error "镜像构建失败: $image_name"
        exit 1
    fi
}

# 主函数
main() {
    log_info "开始构建杏仁解析项目 Docker 镜像"
    
    # 检查环境
    check_docker
    
    # 切换到项目根目录
    cd "$(dirname "$0")/../.."
    
    # 构建杏仁解析服务镜像
    log_info "=== 构建杏仁解析服务镜像 ==="
    build_image \
        "docker/dockerfiles/almond_parser.Dockerfile" \
        "almond-parser:latest" \
        "almond_parser"
    
    # 构建MinerU API镜像
    log_info "=== 构建MinerU API镜像 ==="
    build_image \
        "docker/dockerfiles/mineru_api.Dockerfile" \
        "mineru-api:latest" \
        "mineru_api" \
        "gpu"
    
    # 构建Web前端镜像
    log_info "=== 构建Web前端镜像 ==="
    build_image \
        "docker/dockerfiles/web.Dockerfile" \
        "web-frontend:latest" \
        "web"
    
    # 显示构建结果
    log_info "=== 构建完成，镜像列表 ==="
    docker images | grep -E "(almond-parser|mineru-api|web-frontend)"
    
    log_success "所有镜像构建完成！"
    log_info "可以使用以下命令部署服务："
    echo "  ./scripts/deploy-full.sh          # 完整部署"
    echo "  ./scripts/deploy-management.sh    # 管理平台"
    echo "  ./scripts/deploy-parsing-cluster.sh # 解析集群"
}

# 执行主函数
main "$@"
