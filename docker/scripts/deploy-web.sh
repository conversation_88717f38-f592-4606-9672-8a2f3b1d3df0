#!/bin/bash

# 部署Web前端的脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    # 检查镜像是否存在
    if ! docker images | grep -q "web-frontend"; then
        log_error "web-frontend 镜像不存在，请先运行 ./scripts/build.sh"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 检查网络
check_network() {
    log_info "检查Docker网络"
    
    if ! docker network ls | grep -q "parserflow-network"; then
        log_info "创建网络 parserflow-network"
        docker network create \
            --driver bridge \
            --subnet **********/16 \
            parserflow-network
        log_success "网络创建成功"
    else
        log_success "网络已存在"
    fi
}

# 检查后端服务
check_backend() {
    log_info "检查后端服务"
    
    # 检查杏仁解析服务
    if curl -f http://localhost:8000/health &> /dev/null; then
        log_success "杏仁解析服务运行正常"
    else
        log_warning "杏仁解析服务未运行，Web功能可能受限"
        log_info "可以运行 ./scripts/deploy-almond-parser.sh 启动杏仁解析服务"
    fi
    
    # 检查MinerU API服务
    if curl -f http://localhost:8001/health &> /dev/null; then
        log_success "MinerU API服务运行正常"
    else
        log_warning "MinerU API服务未运行，解析功能可能受限"
        log_info "可以运行 ./scripts/deploy-mineru-api.sh 启动MinerU API服务"
    fi
}

# 部署Web前端
deploy_web() {
    log_info "部署Web前端服务"
    
    # 加载环境变量
    if [ -f "config/.env.web" ]; then
        export $(cat config/.env.web | grep -v '^#' | xargs)
    fi
    
    # 启动服务
    docker-compose -f compose/web.yml up -d
    
    log_success "Web前端服务启动成功"
}

# 更新Nginx配置
update_nginx() {
    log_info "更新Nginx配置"
    
    # 检查Nginx是否运行
    if docker ps | grep -q "parserflow-nginx"; then
        log_info "重新加载Nginx配置"
        docker exec parserflow-nginx nginx -s reload
        log_success "Nginx配置重新加载完成"
    else
        log_warning "Nginx服务未运行，请确保基础设施已部署"
    fi
}

# 等待服务就绪
wait_for_service() {
    log_info "等待Web前端服务就绪..."
    
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost/ &> /dev/null; then
            log_success "Web前端服务已就绪"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "Web前端服务启动超时"
        log_info "查看日志："
        docker-compose -f compose/web.yml logs --tail=20
        exit 1
    fi
}

# 测试Web功能
test_web() {
    log_info "测试Web功能"
    
    # 测试首页
    if curl -f http://localhost/ &> /dev/null; then
        log_success "首页访问正常"
    else
        log_warning "首页访问异常"
    fi
    
    # 测试API代理
    if curl -f http://localhost/api/health &> /dev/null; then
        log_success "API代理正常"
    else
        log_warning "API代理异常，请检查杏仁解析服务"
    fi
}

# 显示服务状态
show_status() {
    log_info "=== 服务状态 ==="
    docker-compose -f compose/web.yml ps
    
    log_info "=== 服务访问地址 ==="
    echo "Web管理平台:   http://localhost"
    echo "API代理:       http://localhost/api"
    echo "MinerU代理:    http://localhost/mineru"
    
    log_info "=== 功能说明 ==="
    echo "- 文档上传和解析"
    echo "- 任务状态监控"
    echo "- 节点管理"
    echo "- 系统监控"
    
    log_info "=== 服务日志 ==="
    echo "查看日志: docker-compose -f compose/web.yml logs -f"
}

# 主函数
main() {
    log_info "开始部署Web前端服务"
    
    # 切换到docker目录
    cd "$(dirname "$0")/.."
    
    # 执行部署步骤
    check_environment
    check_network
    check_backend
    deploy_web
    update_nginx
    wait_for_service
    test_web
    show_status
    
    log_success "Web前端部署完成！"
    log_info "访问地址: http://localhost"
    log_info "如需完整功能，请确保后端服务已启动："
    echo "  ./scripts/deploy-almond-parser.sh  # 杏仁解析服务"
    echo "  ./scripts/deploy-mineru-api.sh     # MinerU API服务"
}

# 执行主函数
main "$@"
