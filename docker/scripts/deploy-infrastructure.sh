#!/bin/bash

# 部署基础设施服务的脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 创建网络
create_network() {
    log_info "创建 Docker 网络"
    
    if docker network ls | grep -q "parserflow-network"; then
        log_warning "网络 parserflow-network 已存在"
    else
        docker network create \
            --driver bridge \
            --subnet 172.20.0.0/16 \
            parserflow-network
        log_success "网络创建成功"
    fi
}

# 创建必要的目录
create_directories() {
    log_info "创建数据目录"
    
    mkdir -p data/mysql
    mkdir -p data/redis
    mkdir -p logs/nginx
    
    log_success "目录创建完成"
}

# 部署基础设施
deploy_infrastructure() {
    log_info "部署基础设施服务"
    
    # 加载环境变量
    if [ -f "config/.env.infrastructure" ]; then
        export $(cat config/.env.infrastructure | grep -v '^#' | xargs)
    fi
    
    # 启动服务
    docker-compose -f compose/infrastructure.yml up -d
    
    log_success "基础设施服务启动成功"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    # 等待MySQL
    log_info "等待 MySQL 启动..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if docker exec parserflow-mysql mysqladmin ping -h localhost --silent; then
            log_success "MySQL 已就绪"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "MySQL 启动超时"
        exit 1
    fi
    
    # 等待Redis
    log_info "等待 Redis 启动..."
    timeout=30
    while [ $timeout -gt 0 ]; do
        if docker exec parserflow-redis redis-cli ping | grep -q "PONG"; then
            log_success "Redis 已就绪"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        log_error "Redis 启动超时"
        exit 1
    fi
    
    # 等待Nginx
    log_info "等待 Nginx 启动..."
    timeout=30
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost/nginx-health &> /dev/null; then
            log_success "Nginx 已就绪"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    if [ $timeout -le 0 ]; then
        log_warning "Nginx 健康检查失败，但服务可能正常"
    fi
}

# 显示服务状态
show_status() {
    log_info "=== 服务状态 ==="
    docker-compose -f compose/infrastructure.yml ps
    
    log_info "=== 服务访问地址 ==="
    echo "MySQL:  localhost:3306"
    echo "Redis:  localhost:6379"
    echo "Nginx:  http://localhost"
}

# 主函数
main() {
    log_info "开始部署基础设施服务"
    
    # 切换到docker目录
    cd "$(dirname "$0")/.."
    
    # 执行部署步骤
    check_environment
    create_network
    create_directories
    deploy_infrastructure
    wait_for_services
    show_status
    
    log_success "基础设施部署完成！"
    log_info "现在可以部署其他服务："
    echo "  ./scripts/deploy-almond-parser.sh  # 部署杏仁解析服务"
    echo "  ./scripts/deploy-mineru-api.sh     # 部署MinerU API"
    echo "  ./scripts/deploy-web.sh            # 部署Web前端"
}

# 执行主函数
main "$@"
