#!/bin/bash

# 扩展MinerU解析节点的脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用说明
show_usage() {
    echo "使用方法: $0 <节点数量> [选项]"
    echo ""
    echo "参数:"
    echo "  节点数量    要扩展到的总节点数量"
    echo ""
    echo "选项:"
    echo "  --gpu-only    仅创建GPU节点"
    echo "  --cpu-only    仅创建CPU节点"
    echo "  --mixed       创建混合节点 (默认)"
    echo "  --help        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 3                # 扩展到3个节点 (混合模式)"
    echo "  $0 5 --gpu-only     # 扩展到5个GPU节点"
    echo "  $0 2 --cpu-only     # 扩展到2个CPU节点"
}

# 检查参数
check_args() {
    if [ $# -eq 0 ] || [ "$1" = "--help" ]; then
        show_usage
        exit 0
    fi
    
    if ! [[ "$1" =~ ^[0-9]+$ ]] || [ "$1" -lt 1 ]; then
        log_error "节点数量必须是大于0的整数"
        show_usage
        exit 1
    fi
    
    TARGET_NODES=$1
    NODE_TYPE=${2:-"--mixed"}
    
    log_info "目标节点数量: $TARGET_NODES"
    log_info "节点类型: $NODE_TYPE"
}

# 检查环境
check_environment() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    # 检查镜像是否存在
    if ! docker images | grep -q "mineru-api"; then
        log_error "mineru-api 镜像不存在，请先运行 ./scripts/build.sh"
        exit 1
    fi
    
    # 检查网络
    if ! docker network ls | grep -q "parserflow-network"; then
        log_error "网络 parserflow-network 不存在，请先部署基础设施"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 检查GPU支持
check_gpu_support() {
    if [ "$NODE_TYPE" = "--gpu-only" ] || [ "$NODE_TYPE" = "--mixed" ]; then
        if command -v nvidia-smi &> /dev/null; then
            GPU_COUNT=$(nvidia-smi --query-gpu=count --format=csv,noheader,nounits | head -1)
            log_info "检测到 $GPU_COUNT 个GPU"
            
            if docker run --rm --gpus all nvidia/cuda:11.8-base nvidia-smi &> /dev/null; then
                log_success "Docker GPU支持正常"
                export GPU_AVAILABLE=true
            else
                log_warning "Docker GPU支持异常"
                export GPU_AVAILABLE=false
            fi
        else
            log_warning "未检测到NVIDIA GPU"
            export GPU_AVAILABLE=false
            
            if [ "$NODE_TYPE" = "--gpu-only" ]; then
                log_error "无法创建GPU节点，因为GPU不可用"
                exit 1
            fi
        fi
    else
        export GPU_AVAILABLE=false
    fi
}

# 获取当前运行的节点
get_current_nodes() {
    CURRENT_NODES=$(docker ps --filter "name=parserflow-mineru-api" --format "table {{.Names}}" | grep -v NAMES | wc -l)
    log_info "当前运行的MinerU节点数量: $CURRENT_NODES"
}

# 生成动态compose文件
generate_compose_file() {
    local compose_file="compose/mineru-scale-$TARGET_NODES.yml"
    
    log_info "生成扩展配置文件: $compose_file"
    
    cat > "$compose_file" << 'EOF'
version: '3.8'

services:
EOF

    # 根据节点类型和数量生成服务配置
    for i in $(seq 1 $TARGET_NODES); do
        local service_name="mineru-api-$i"
        local container_name="parserflow-mineru-api-$i"
        local port=$((8000 + i))
        
        # 确定节点类型
        local is_gpu_node=false
        if [ "$NODE_TYPE" = "--gpu-only" ]; then
            is_gpu_node=true
        elif [ "$NODE_TYPE" = "--mixed" ] && [ "$GPU_AVAILABLE" = "true" ]; then
            # 混合模式：前一半是GPU节点，后一半是CPU节点
            if [ $i -le $((TARGET_NODES / 2 + TARGET_NODES % 2)) ]; then
                is_gpu_node=true
            fi
        fi
        
        cat >> "$compose_file" << EOF
  $service_name:
    image: mineru-api:latest
    container_name: $container_name
    restart: unless-stopped
    environment:
      HOST: 0.0.0.0
      PORT: 8001
      DEBUG: \${DEBUG:-false}
EOF

        if [ "$is_gpu_node" = "true" ]; then
            local gpu_id=$((i - 1))
            cat >> "$compose_file" << EOF
      CUDA_VISIBLE_DEVICES: "$gpu_id"
      NVIDIA_VISIBLE_DEVICES: "$gpu_id"
      NODE_TYPE: "sglang"
      VLM_SERVER_URL: \${VLM_SERVER_URL_$i:-http://127.0.0.1:$((30000 + gpu_id))}
EOF
        else
            cat >> "$compose_file" << EOF
      NODE_TYPE: "pipeline"
      PIPELINE_MODE: "pipeline"
EOF
        fi
        
        cat >> "$compose_file" << EOF
      MINERU_CONFIG_PATH: /app/config/mineru_config.json
      VLM_MODEL_PATH: \${VLM_MODEL_PATH:-/app/models}
      LIBREOFFICE_PATH: /usr/bin/libreoffice
      ENABLE_DOC_CONVERSION: \${ENABLE_DOC_CONVERSION:-true}
      TEMP_DIR: /app/temp
      OUTPUT_DIR: /app/output
      MAX_FILE_SIZE: \${MAX_FILE_SIZE:-100MB}
      LOG_LEVEL: \${LOG_LEVEL:-INFO}
      LOG_DIR: /app/logs
      MAX_CONCURRENT_REQUESTS: \${MAX_CONCURRENT_REQUESTS:-5}
      REQUEST_TIMEOUT: \${REQUEST_TIMEOUT:-300}
      API_KEY_REQUIRED: \${API_KEY_REQUIRED:-false}
      API_KEY: \${API_KEY:-}
      NODE_ID: $service_name
    ports:
      - "\${MINERU_API_PORT_$i:-$port}:8001"
    volumes:
      - mineru_temp_$i:/app/temp
      - mineru_output_$i:/app/output
      - mineru_logs_$i:/app/logs
      - mineru_models:/app/models:ro
      - mineru_config:/app/config:ro
    networks:
      - parserflow-network
EOF

        if [ "$is_gpu_node" = "true" ]; then
            cat >> "$compose_file" << EOF
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              device_ids: ["$((i - 1))"]
              capabilities: [gpu]
EOF
        fi
        
        cat >> "$compose_file" << EOF
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

EOF
    done
    
    # 添加volumes配置
    cat >> "$compose_file" << 'EOF'
volumes:
EOF
    
    for i in $(seq 1 $TARGET_NODES); do
        cat >> "$compose_file" << EOF
  mineru_temp_$i:
    driver: local
  mineru_output_$i:
    driver: local
  mineru_logs_$i:
    driver: local
EOF
    done
    
    cat >> "$compose_file" << 'EOF'
  mineru_models:
    external: true
  mineru_config:
    external: true

networks:
  parserflow-network:
    external: true
EOF

    log_success "配置文件生成完成"
}

# 部署扩展节点
deploy_scaled_nodes() {
    local compose_file="compose/mineru-scale-$TARGET_NODES.yml"
    
    log_info "部署 $TARGET_NODES 个MinerU节点"
    
    # 加载环境变量
    if [ -f "config/.env.mineru-api" ]; then
        export $(cat config/.env.mineru-api | grep -v '^#' | xargs)
    fi
    
    # 停止现有节点
    if [ $CURRENT_NODES -gt 0 ]; then
        log_info "停止现有节点"
        docker ps --filter "name=parserflow-mineru-api" --format "{{.Names}}" | xargs -r docker stop
        docker ps -a --filter "name=parserflow-mineru-api" --format "{{.Names}}" | xargs -r docker rm
    fi
    
    # 启动新节点
    docker-compose -f "$compose_file" up -d
    
    log_success "节点部署完成"
}

# 等待节点就绪
wait_for_nodes() {
    log_info "等待节点就绪..."
    
    local ready_count=0
    local timeout=300  # 5分钟超时
    
    while [ $timeout -gt 0 ] && [ $ready_count -lt $TARGET_NODES ]; do
        ready_count=0
        
        for i in $(seq 1 $TARGET_NODES); do
            local port=$((8000 + i))
            if curl -f "http://localhost:$port/health" &> /dev/null; then
                ready_count=$((ready_count + 1))
            fi
        done
        
        log_info "就绪节点: $ready_count/$TARGET_NODES"
        
        if [ $ready_count -lt $TARGET_NODES ]; then
            sleep 10
            timeout=$((timeout - 10))
        fi
    done
    
    if [ $ready_count -eq $TARGET_NODES ]; then
        log_success "所有节点已就绪"
    else
        log_warning "部分节点未就绪 ($ready_count/$TARGET_NODES)"
    fi
}

# 显示节点状态
show_nodes_status() {
    local compose_file="compose/mineru-scale-$TARGET_NODES.yml"
    
    log_info "=== MinerU节点状态 ==="
    docker-compose -f "$compose_file" ps
    
    log_info "=== 节点访问地址 ==="
    for i in $(seq 1 $TARGET_NODES); do
        local port=$((8000 + i))
        local status="❌"
        if curl -f "http://localhost:$port/health" &> /dev/null; then
            status="✅"
        fi
        echo "$status MinerU节点-$i: http://localhost:$port"
    done
    
    log_info "=== GPU使用情况 ==="
    if [ "$GPU_AVAILABLE" = "true" ]; then
        nvidia-smi --query-gpu=index,name,utilization.gpu,memory.used,memory.total --format=csv,noheader
    else
        echo "GPU不可用，所有节点运行在CPU模式"
    fi
    
    log_info "=== 管理命令 ==="
    echo "查看日志: docker-compose -f $compose_file logs -f"
    echo "重启节点: docker-compose -f $compose_file restart"
    echo "停止节点: docker-compose -f $compose_file down"
}

# 主函数
main() {
    log_info "MinerU节点扩展工具"
    
    # 切换到docker目录
    cd "$(dirname "$0")/.."
    
    # 执行扩展步骤
    check_args "$@"
    check_environment
    check_gpu_support
    get_current_nodes
    generate_compose_file
    deploy_scaled_nodes
    wait_for_nodes
    show_nodes_status
    
    log_success "MinerU节点扩展完成！"
    log_info "当前运行 $TARGET_NODES 个解析节点"
    log_info "节点类型: $NODE_TYPE"
}

# 执行主函数
main "$@"
