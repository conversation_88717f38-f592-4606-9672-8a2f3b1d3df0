@echo off
REM 完整部署脚本（所有服务）- Windows版本

setlocal enabledelayedexpansion

echo [INFO] 开始部署杏仁解析完整系统
echo [INFO] 包含所有服务: 基础设施 + 杏仁解析 + MinerU API + Web前端

REM 检查环境
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker 未安装
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose 未安装
    pause
    exit /b 1
)

REM 检查镜像是否存在
set missing_images=
docker images | findstr "almond-parser" >nul || set missing_images=!missing_images! almond-parser
docker images | findstr "mineru-api" >nul || set missing_images=!missing_images! mineru-api
docker images | findstr "web-frontend" >nul || set missing_images=!missing_images! web-frontend

if not "!missing_images!"=="" (
    echo [ERROR] 缺少镜像: !missing_images!
    echo [ERROR] 请先运行 scripts\build.bat 构建镜像
    pause
    exit /b 1
)

echo [SUCCESS] 环境检查通过

REM 检查GPU支持
nvidia-smi >nul 2>&1
if errorlevel 1 (
    echo [WARNING] 未检测到NVIDIA GPU，MinerU将使用CPU模式
    set GPU_ENABLED=false
) else (
    echo [INFO] 检测到NVIDIA GPU
    nvidia-smi --query-gpu=name,memory.total --format=csv,noheader
    
    docker run --rm --gpus all nvidia/cuda:11.8-base nvidia-smi >nul 2>&1
    if errorlevel 1 (
        echo [WARNING] Docker GPU支持异常，MinerU将使用CPU模式
        set GPU_ENABLED=false
    ) else (
        echo [SUCCESS] Docker GPU支持正常
        set GPU_ENABLED=true
    )
)

REM 切换到docker目录
cd /d "%~dp0\.."

REM 创建必要的目录
echo [INFO] 创建数据目录
if not exist "data\mysql" mkdir data\mysql
if not exist "data\redis" mkdir data\redis
if not exist "data\uploads" mkdir data\uploads
if not exist "data\output" mkdir data\output
if not exist "data\mineru\temp" mkdir data\mineru\temp
if not exist "data\mineru\output" mkdir data\mineru\output
if not exist "data\mineru\models" mkdir data\mineru\models
if not exist "data\mineru\config" mkdir data\mineru\config
if not exist "logs\mysql" mkdir logs\mysql
if not exist "logs\redis" mkdir logs\redis
if not exist "logs\almond" mkdir logs\almond
if not exist "logs\mineru" mkdir logs\mineru
if not exist "logs\nginx" mkdir logs\nginx

echo [SUCCESS] 目录创建完成

REM 创建MinerU配置文件
echo [INFO] 创建MinerU配置文件
if not exist "data\mineru\config\mineru_config.json" (
    echo { > data\mineru\config\mineru_config.json
    echo     "model": { >> data\mineru\config\mineru_config.json
    echo         "ocr": { >> data\mineru\config\mineru_config.json
    echo             "provider": "paddle", >> data\mineru\config\mineru_config.json
    echo             "config": { >> data\mineru\config\mineru_config.json
    echo                 "use_gpu": !GPU_ENABLED! >> data\mineru\config\mineru_config.json
    echo             } >> data\mineru\config\mineru_config.json
    echo         }, >> data\mineru\config\mineru_config.json
    echo         "layout": { >> data\mineru\config\mineru_config.json
    echo             "provider": "layoutlmv3", >> data\mineru\config\mineru_config.json
    echo             "config": { >> data\mineru\config\mineru_config.json
    echo                 "use_gpu": !GPU_ENABLED! >> data\mineru\config\mineru_config.json
    echo             } >> data\mineru\config\mineru_config.json
    echo         } >> data\mineru\config\mineru_config.json
    echo     }, >> data\mineru\config\mineru_config.json
    echo     "output": { >> data\mineru\config\mineru_config.json
    echo         "format": "markdown", >> data\mineru\config\mineru_config.json
    echo         "save_images": true >> data\mineru\config\mineru_config.json
    echo     }, >> data\mineru\config\mineru_config.json
    echo     "processing": { >> data\mineru\config\mineru_config.json
    echo         "max_workers": 4, >> data\mineru\config\mineru_config.json
    echo         "chunk_size": 1024 >> data\mineru\config\mineru_config.json
    echo     } >> data\mineru\config\mineru_config.json
    echo } >> data\mineru\config\mineru_config.json
    echo [SUCCESS] MinerU配置文件创建完成
) else (
    echo [INFO] MinerU配置文件已存在
)

REM 部署完整系统
echo [INFO] 部署完整系统
if "!GPU_ENABLED!"=="true" (
    echo [INFO] 使用GPU模式部署
    docker-compose -f compose/full.yml up -d
) else (
    echo [INFO] 使用CPU模式部署
    REM 创建CPU版本的compose文件
    powershell -Command "(Get-Content compose/full.yml) -replace 'nvidia', '# nvidia' | Set-Content compose/full-cpu.yml"
    docker-compose -f compose/full-cpu.yml up -d
)

if errorlevel 1 (
    echo [ERROR] 系统部署失败
    pause
    exit /b 1
)

echo [SUCCESS] 完整系统启动成功

REM 等待服务就绪
echo [INFO] 等待服务就绪...

REM 等待MySQL
echo [INFO] 等待 MySQL 启动...
set timeout=60
:wait_mysql
docker exec parserflow-mysql mysqladmin ping -h localhost --silent >nul 2>&1
if not errorlevel 1 goto mysql_ready
timeout /t 3 /nobreak >nul
set /a timeout-=3
if !timeout! gtr 0 goto wait_mysql
echo [ERROR] MySQL 启动超时
pause
exit /b 1
:mysql_ready
echo [SUCCESS] MySQL 已就绪

REM 等待Redis
echo [INFO] 等待 Redis 启动...
set timeout=30
:wait_redis
docker exec parserflow-redis redis-cli ping >nul 2>&1 | findstr "PONG" >nul
if not errorlevel 1 goto redis_ready
timeout /t 2 /nobreak >nul
set /a timeout-=2
if !timeout! gtr 0 goto wait_redis
echo [ERROR] Redis 启动超时
pause
exit /b 1
:redis_ready
echo [SUCCESS] Redis 已就绪

REM 等待杏仁解析服务
echo [INFO] 等待杏仁解析服务启动...
set timeout=120
:wait_almond
curl -f http://localhost:8000/health >nul 2>&1
if not errorlevel 1 goto almond_ready
timeout /t 3 /nobreak >nul
set /a timeout-=3
if !timeout! gtr 0 goto wait_almond
echo [ERROR] 杏仁解析服务启动超时
pause
exit /b 1
:almond_ready
echo [SUCCESS] 杏仁解析服务已就绪

REM 等待MinerU API
echo [INFO] 等待MinerU API启动...
set timeout=180
:wait_mineru
curl -f http://localhost:8001/health >nul 2>&1
if not errorlevel 1 goto mineru_ready
timeout /t 5 /nobreak >nul
set /a timeout-=5
if !timeout! gtr 0 goto wait_mineru
echo [ERROR] MinerU API启动超时
pause
exit /b 1
:mineru_ready
echo [SUCCESS] MinerU API已就绪

REM 等待Web前端
echo [INFO] 等待Web前端启动...
set timeout=60
:wait_web
curl -f http://localhost/ >nul 2>&1
if not errorlevel 1 goto web_ready
timeout /t 2 /nobreak >nul
set /a timeout-=2
if !timeout! gtr 0 goto wait_web
echo [WARNING] Web前端启动超时，但可能正常运行
:web_ready
echo [SUCCESS] Web前端已就绪

REM 显示完整系统状态
echo [INFO] === 完整系统服务状态 ===
docker-compose -f compose/full.yml ps

echo [INFO] === 服务访问地址 ===
echo 🌐 Web管理平台:   http://localhost
echo 🔧 杏仁解析API:   http://localhost:8000
echo ⚡ MinerU API:    http://localhost:8001
echo 📚 API文档:       http://localhost:8000/docs
echo 🗄️  MySQL:        localhost:3306
echo 🔴 Redis:         localhost:6379

echo [INFO] === 系统功能 ===
echo ✅ 文档上传和解析
echo ✅ 任务状态监控
echo ✅ 节点管理
echo ✅ 系统监控
echo ✅ GPU加速解析 (如果可用)
echo ✅ LibreOffice文档转换

echo [INFO] === GPU状态 ===
if "!GPU_ENABLED!"=="true" (
    echo GPU模式: 启用
    docker exec parserflow-mineru-api nvidia-smi --query-gpu=name,utilization.gpu,memory.used,memory.total --format=csv,noheader 2>nul || echo GPU信息获取失败
) else (
    echo GPU模式: 禁用 (CPU模式)
)

echo [SUCCESS] 🎉 完整系统部署成功！
echo [INFO] 🌐 访问地址: http://localhost
echo [INFO] 📚 API文档: http://localhost:8000/docs
echo.
echo [INFO] 💡 系统已就绪，您可以：
echo   1. 访问 Web 管理平台开始使用
echo   2. 上传文档进行解析测试
echo   3. 监控系统运行状态
echo   4. 根据需要扩展解析节点

pause
