@echo off
REM 构建所有Docker镜像的Windows批处理脚本

setlocal enabledelayedexpansion

echo [INFO] 开始构建杏仁解析项目 Docker 镜像

REM 检查Docker是否安装
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker 未安装，请先安装 Docker Desktop
    pause
    exit /b 1
)

REM 检查Docker是否运行
docker info >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker 服务未启动，请启动 Docker Desktop
    pause
    exit /b 1
)

echo [SUCCESS] Docker 环境检查通过

REM 切换到项目根目录
cd /d "%~dp0..\.."

REM 构建杏仁解析服务镜像
echo [INFO] === 构建杏仁解析服务镜像 ===
docker build -f docker/dockerfiles/almond_parser.Dockerfile -t almond-parser:latest almond_parser
if errorlevel 1 (
    echo [ERROR] 杏仁解析服务镜像构建失败
    pause
    exit /b 1
)
echo [SUCCESS] 杏仁解析服务镜像构建成功

REM 构建MinerU API镜像
echo [INFO] === 构建MinerU API镜像 ===
docker build --build-arg PLATFORM=linux -f docker/dockerfiles/mineru_api.Dockerfile -t mineru-api:latest mineru_api
if errorlevel 1 (
    echo [ERROR] MinerU API镜像构建失败
    pause
    exit /b 1
)
echo [SUCCESS] MinerU API镜像构建成功

REM 构建Web前端镜像
echo [INFO] === 构建Web前端镜像 ===
docker build -f docker/dockerfiles/web.Dockerfile -t web-frontend:latest web
if errorlevel 1 (
    echo [ERROR] Web前端镜像构建失败
    pause
    exit /b 1
)
echo [SUCCESS] Web前端镜像构建成功

REM 显示构建结果
echo [INFO] === 构建完成，镜像列表 ===
docker images | findstr "almond-parser mineru-api web-frontend"

echo [SUCCESS] 所有镜像构建完成！
echo [INFO] 可以使用以下命令部署服务：
echo   scripts\deploy-full.bat          # 完整部署
echo   scripts\deploy-management.bat    # 管理平台
echo   scripts\deploy-mineru-api.bat    # 解析引擎

pause
