#!/bin/bash

# 完整部署脚本（所有服务）

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    # 检查镜像是否存在
    missing_images=()
    if ! docker images | grep -q "almond-parser"; then
        missing_images+=("almond-parser")
    fi
    if ! docker images | grep -q "mineru-api"; then
        missing_images+=("mineru-api")
    fi
    if ! docker images | grep -q "web-frontend"; then
        missing_images+=("web-frontend")
    fi
    
    if [ ${#missing_images[@]} -gt 0 ]; then
        log_error "缺少镜像: ${missing_images[*]}"
        log_error "请先运行 ./scripts/build.sh 构建镜像"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 检查GPU支持
check_gpu() {
    log_info "检查GPU支持"
    
    if command -v nvidia-smi &> /dev/null; then
        log_info "检测到NVIDIA GPU："
        nvidia-smi --query-gpu=name,memory.total --format=csv,noheader
        
        if docker run --rm --gpus all nvidia/cuda:11.8-base nvidia-smi &> /dev/null; then
            log_success "Docker GPU支持正常"
            export GPU_ENABLED=true
        else
            log_warning "Docker GPU支持异常，MinerU将使用CPU模式"
            export GPU_ENABLED=false
        fi
    else
        log_warning "未检测到NVIDIA GPU，MinerU将使用CPU模式"
        export GPU_ENABLED=false
    fi
}

# 创建必要的目录
create_directories() {
    log_info "创建数据目录"
    
    mkdir -p data/mysql
    mkdir -p data/redis
    mkdir -p data/uploads
    mkdir -p data/output
    mkdir -p data/mineru/temp
    mkdir -p data/mineru/output
    mkdir -p data/mineru/models
    mkdir -p data/mineru/config
    mkdir -p logs/mysql
    mkdir -p logs/redis
    mkdir -p logs/almond
    mkdir -p logs/mineru
    mkdir -p logs/nginx
    
    log_success "目录创建完成"
}

# 创建MinerU配置
create_mineru_config() {
    log_info "创建MinerU配置文件"
    
    config_file="data/mineru/config/mineru_config.json"
    
    if [ ! -f "$config_file" ]; then
        cat > "$config_file" << EOF
{
    "model": {
        "ocr": {
            "provider": "paddle",
            "config": {
                "use_gpu": ${GPU_ENABLED:-false}
            }
        },
        "layout": {
            "provider": "layoutlmv3",
            "config": {
                "use_gpu": ${GPU_ENABLED:-false}
            }
        }
    },
    "output": {
        "format": "markdown",
        "save_images": true
    },
    "processing": {
        "max_workers": 4,
        "chunk_size": 1024
    }
}
EOF
        log_success "MinerU配置文件创建完成"
    else
        log_info "MinerU配置文件已存在"
    fi
}

# 部署完整系统
deploy_full_system() {
    log_info "部署完整系统"
    log_info "包含服务: MySQL + Redis + 杏仁解析 + MinerU API + Web前端 + Nginx"
    
    # 加载所有环境变量
    if [ -f "config/.env.infrastructure" ]; then
        export $(cat config/.env.infrastructure | grep -v '^#' | xargs)
    fi
    if [ -f "config/.env.almond-parser" ]; then
        export $(cat config/.env.almond-parser | grep -v '^#' | xargs)
    fi
    if [ -f "config/.env.mineru-api" ]; then
        export $(cat config/.env.mineru-api | grep -v '^#' | xargs)
    fi
    if [ -f "config/.env.web" ]; then
        export $(cat config/.env.web | grep -v '^#' | xargs)
    fi
    
    # 启动完整系统
    if [ "$GPU_ENABLED" = "true" ]; then
        log_info "使用GPU模式部署"
        docker-compose -f compose/full.yml up -d
    else
        log_info "使用CPU模式部署"
        # 创建CPU版本的compose文件
        sed 's/nvidia/# nvidia/g' compose/full.yml > compose/full-cpu.yml
        docker-compose -f compose/full-cpu.yml up -d
    fi
    
    log_success "完整系统启动成功"
}

# 等待所有服务就绪
wait_for_all_services() {
    log_info "等待所有服务就绪..."
    
    # 等待MySQL
    log_info "等待 MySQL 启动..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if docker exec parserflow-mysql mysqladmin ping -h localhost --silent 2>/dev/null; then
            log_success "MySQL 已就绪"
            break
        fi
        sleep 3
        timeout=$((timeout-3))
    done
    
    # 等待Redis
    log_info "等待 Redis 启动..."
    timeout=30
    while [ $timeout -gt 0 ]; do
        if docker exec parserflow-redis redis-cli ping 2>/dev/null | grep -q "PONG"; then
            log_success "Redis 已就绪"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
    
    # 等待杏仁解析服务
    log_info "等待杏仁解析服务启动..."
    timeout=120
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:8000/health &> /dev/null; then
            log_success "杏仁解析服务已就绪"
            break
        fi
        sleep 3
        timeout=$((timeout-3))
    done
    
    # 等待MinerU API
    log_info "等待MinerU API启动..."
    timeout=180  # GPU模式需要更长时间
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:8001/health &> /dev/null; then
            log_success "MinerU API已就绪"
            break
        fi
        sleep 5
        timeout=$((timeout-5))
    done
    
    # 等待Web前端
    log_info "等待Web前端启动..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost/ &> /dev/null; then
            log_success "Web前端已就绪"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done
}

# 测试完整系统
test_full_system() {
    log_info "测试完整系统功能"
    
    # 测试各个服务
    services=(
        "http://localhost:8000/health:杏仁解析API"
        "http://localhost:8001/health:MinerU API"
        "http://localhost/:Web前端"
        "http://localhost/api/health:API代理"
    )
    
    for service in "${services[@]}"; do
        url="${service%:*}"
        name="${service#*:}"
        
        if curl -f "$url" &> /dev/null; then
            log_success "$name 测试通过"
        else
            log_warning "$name 测试失败"
        fi
    done
}

# 显示完整系统状态
show_full_status() {
    log_info "=== 完整系统服务状态 ==="
    docker-compose -f compose/full.yml ps
    
    log_info "=== 服务访问地址 ==="
    echo "🌐 Web管理平台:   http://localhost"
    echo "🔧 杏仁解析API:   http://localhost:8000"
    echo "⚡ MinerU API:    http://localhost:8001"
    echo "📚 API文档:       http://localhost:8000/docs"
    echo "🗄️  MySQL:        localhost:3306"
    echo "🔴 Redis:         localhost:6379"
    
    log_info "=== 系统功能 ==="
    echo "✅ 文档上传和解析"
    echo "✅ 任务状态监控"
    echo "✅ 节点管理"
    echo "✅ 系统监控"
    echo "✅ GPU加速解析 (如果可用)"
    echo "✅ LibreOffice文档转换"
    
    log_info "=== GPU状态 ==="
    if [ "$GPU_ENABLED" = "true" ]; then
        echo "GPU模式: 启用"
        docker exec parserflow-mineru-api nvidia-smi --query-gpu=name,utilization.gpu,memory.used,memory.total --format=csv,noheader 2>/dev/null || echo "GPU信息获取失败"
    else
        echo "GPU模式: 禁用 (CPU模式)"
    fi
    
    log_info "=== 数据目录 ==="
    echo "MySQL数据:     ./data/mysql"
    echo "Redis数据:     ./data/redis"
    echo "上传文件:      ./data/uploads"
    echo "输出文件:      ./data/output"
    echo "MinerU数据:    ./data/mineru"
    echo "日志文件:      ./logs/"
    
    log_info "=== 管理命令 ==="
    echo "查看日志:      docker-compose -f compose/full.yml logs -f"
    echo "重启服务:      docker-compose -f compose/full.yml restart"
    echo "停止服务:      docker-compose -f compose/full.yml down"
    echo "扩展解析节点:  ./scripts/scale-mineru.sh 3"
}

# 主函数
main() {
    log_info "🚀 开始部署杏仁解析完整系统"
    log_info "包含所有服务: 基础设施 + 杏仁解析 + MinerU API + Web前端"
    
    # 切换到docker目录
    cd "$(dirname "$0")/.."
    
    # 执行部署步骤
    check_environment
    check_gpu
    create_directories
    create_mineru_config
    deploy_full_system
    wait_for_all_services
    test_full_system
    show_full_status
    
    log_success "🎉 完整系统部署成功！"
    log_info "🌐 访问地址: http://localhost"
    log_info "📚 API文档: http://localhost:8000/docs"
    log_info ""
    log_info "💡 系统已就绪，您可以："
    echo "  1. 访问 Web 管理平台开始使用"
    echo "  2. 上传文档进行解析测试"
    echo "  3. 监控系统运行状态"
    echo "  4. 根据需要扩展解析节点"
}

# 执行主函数
main "$@"
