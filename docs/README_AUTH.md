# MineruAPI 认证系统

## 🎯 概述

为MineruAPI服务添加了完整的认证系统，支持API key认证、速率限制、IP白名单等功能，并为未来与杏仁解析系统的集成预留了接口。

## ✨ 功能特性

### 🔐 认证功能
- **全面保护**: 保护所有API接口，包括LitServer的 `/predict` 接口
- **API Key认证**: 支持Bearer token和X-API-Key头
- **多种传递方式**: 请求头、查询参数
- **灵活配置**: 可启用/禁用认证功能

### 🏗️ 多后端支持
- **文件后端**: JSON文件存储，适合开发和单机部署
- **SQLite后端**: 数据库存储，支持并发访问
- **远程后端**: HTTP API调用，支持与杏仁解析系统集成

### 🛡️ 安全功能
- **速率限制**: 防止API滥用，可配置每分钟请求数
- **IP白名单**: 限制特定IP地址访问
- **过期管理**: 支持API key过期时间设置
- **状态管理**: 支持撤销、禁用API key

### 📊 管理功能
- **使用统计**: 跟踪API使用次数和最后使用时间
- **批量管理**: 支持列出、创建、撤销API key
- **自动清理**: 定期清理过期的API key

## 🚀 快速开始

### 1. 启用认证

编辑 `.env` 文件：

```bash
# 启用认证
ENABLE_AUTH=true
AUTH_BACKEND=file
AUTH_FILE_PATH=api_keys.json
AUTH_ENABLE_RATE_LIMIT=true
AUTH_DEFAULT_RATE_LIMIT=100
```

### 2. 初始化认证系统

```bash
# 创建第一个管理员API key
python scripts/init_auth.py
```

### 3. 启动服务

```bash
python mineru_api/start_server.py
```

### 4. 测试认证

```bash
# 测试 /predict 接口保护
curl -X POST http://localhost:8000/predict \
     -H "Content-Type: application/json" \
     -d '{"file_name": "test.pdf"}'
# 应该返回 401 Unauthorized

# 使用API key访问
curl -H "Authorization: Bearer your-api-key" \
     http://localhost:8000/auth/statistics

# 测试认证保护
python mineru_api/tests/test_predict_auth.py
```

## 📋 API Key管理

### 命令行工具

```bash
# 创建API key
python -m mineru_api.cli.auth_cli create "我的应用"

# 列出API keys
python -m mineru_api.cli.auth_cli list

# 撤销API key
python -m mineru_api.cli.auth_cli revoke mk_xxxxxxxxxx

# 查看统计
python -m mineru_api.cli.auth_cli stats

# 清理过期key
python -m mineru_api.cli.auth_cli cleanup
```

### HTTP API

```bash
# 创建API key
curl -X POST "http://localhost:8000/auth/keys" \
  -H "Authorization: Bearer admin-api-key" \
  -H "Content-Type: application/json" \
  -d '{"name": "新应用", "expires_days": 30}'

# 列出API keys
curl -H "Authorization: Bearer admin-api-key" \
     http://localhost:8000/auth/keys

# 撤销API key
curl -X DELETE "http://localhost:8000/auth/keys/mk_xxxxxxxxxx" \
     -H "Authorization: Bearer admin-api-key"
```

## 🏢 部署场景

### 场景1: 单独部署mineru-api

```bash
# 使用文件后端，简单配置
ENABLE_AUTH=true
AUTH_BACKEND=file
AUTH_FILE_PATH=api_keys.json
```

### 场景2: 与杏仁解析联合部署

```bash
# mineru-api配置
ENABLE_AUTH=true
AUTH_BACKEND=remote
AUTH_REMOTE_URL=http://localhost:9000/api
AUTH_REMOTE_API_KEY=shared-secret

# 杏仁解析系统提供认证API
# GET  /api/auth/verify
# POST /api/auth/create
# DELETE /api/auth/revoke/{key}
```

### 场景3: 共享数据库部署

```bash
# 两个服务共享SQLite数据库
ENABLE_AUTH=true
AUTH_BACKEND=sqlite
AUTH_SQLITE_PATH=/shared/data/auth.db
```

## 🔧 配置选项

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `ENABLE_AUTH` | `true` | 启用认证功能 |
| `AUTH_BACKEND` | `file` | 后端类型: file/sqlite/remote |
| `AUTH_FILE_PATH` | `api_keys.json` | 文件后端路径 |
| `AUTH_SQLITE_PATH` | `auth.db` | SQLite数据库路径 |
| `AUTH_REMOTE_URL` | - | 远程认证服务URL |
| `AUTH_REMOTE_API_KEY` | - | 远程服务API key |
| `AUTH_ENABLE_RATE_LIMIT` | `true` | 启用速率限制 |
| `AUTH_DEFAULT_RATE_LIMIT` | `100` | 默认速率限制(每分钟) |

### 后端配置

#### 文件后端
- 使用JSON文件存储
- 适合开发环境和小规模部署
- 无需额外依赖

#### SQLite后端
- 使用SQLite数据库
- 支持并发访问
- 适合中等规模部署

#### 远程后端
- 通过HTTP API调用远程服务
- 支持集中化管理
- 适合微服务架构

## 🔌 客户端集成

### Python客户端

```python
import httpx

class MineruAPIClient:
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url
        self.headers = {"Authorization": f"Bearer {api_key}"}
    
    async def parse_file(self, file_path: str):
        async with httpx.AsyncClient() as client:
            with open(file_path, 'rb') as f:
                response = await client.post(
                    f"{self.base_url}/parse/upload",
                    headers=self.headers,
                    files={"file": f}
                )
            return response.json()
```

### JavaScript客户端

```javascript
class MineruAPIClient {
    constructor(baseUrl, apiKey) {
        this.baseUrl = baseUrl;
        this.headers = {
            'Authorization': `Bearer ${apiKey}`
        };
    }
    
    async parseFile(file) {
        const formData = new FormData();
        formData.append('file', file);
        
        const response = await fetch(`${this.baseUrl}/parse/upload`, {
            method: 'POST',
            headers: this.headers,
            body: formData
        });
        
        return response.json();
    }
}
```

## 🧪 测试

### 运行测试

```bash
# 运行认证功能测试
python mineru_api/tests/test_auth.py

# 运行pytest测试
pytest mineru_api/tests/test_auth.py -v
```

### 集成测试

```bash
# 启动服务
python mineru_api/start_server.py

# 在另一个终端运行集成测试
python mineru_api/tests/test_auth.py
```

## 📁 文件结构

```
mineru_api/
├── auth/                          # 认证模块
│   ├── __init__.py
│   ├── models.py                  # 数据模型
│   ├── manager.py                 # 认证管理器
│   ├── middleware.py              # 认证中间件
│   └── backends/                  # 认证后端
│       ├── __init__.py
│       ├── base.py               # 基类
│       ├── file_backend.py       # 文件后端
│       ├── sqlite_backend.py     # SQLite后端
│       └── remote_backend.py     # 远程后端
├── api/
│   ├── __init__.py               # 主API路由(已添加认证)
│   └── auth_routes.py            # 认证管理API
├── cli/
│   ├── __init__.py
│   └── auth_cli.py               # 命令行工具
├── tests/
│   └── test_auth.py              # 认证测试
├── config.py                     # 配置(已更新)
├── main.py                       # 主程序(已更新)
└── .env                          # 环境配置(已更新)

scripts/
└── init_auth.py                  # 初始化脚本

docs/
└── authentication.md            # 详细文档
```

## 🔒 安全建议

1. **保护API Key**: 不要在代码中硬编码
2. **使用HTTPS**: 生产环境必须使用HTTPS
3. **定期轮换**: 定期更新API key
4. **监控使用**: 定期检查使用统计
5. **备份数据**: 定期备份认证数据
6. **权限控制**: 为不同用途创建不同权限的API key

## 🚧 未来扩展

### 与杏仁解析系统集成
- 杏仁解析系统作为认证中心
- 统一的API key管理界面
- 跨服务的使用统计

### 高级功能
- 基于角色的访问控制(RBAC)
- OAuth2.0支持
- JWT token认证
- 审计日志

## 📞 支持

如需帮助或有问题，请：
1. 查看 `docs/authentication.md` 详细文档
2. 运行测试脚本验证配置
3. 检查日志文件排查问题

---

**注意**: 认证功能可以通过 `ENABLE_AUTH=false` 完全禁用，保持向后兼容性。
