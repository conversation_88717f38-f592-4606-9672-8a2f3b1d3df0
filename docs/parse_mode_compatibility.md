# 解析模式兼容性优化

## 🎯 问题背景

在之前的实现中，节点选择逻辑过于严格：
- 节点配置为 `sglang` 模式时，只接受 `sglang` 请求
- 当用户请求 `pipeline` 模式时，即使有 VLM 节点可用，也会被拒绝
- 造成资源浪费和"无可用节点"的错误

## 💡 解决方案

### 核心思想
**VLM 节点具备向下兼容能力**：
- VLM/SGLang 节点可以处理 `pipeline` 请求
- Pipeline 节点只能处理 `pipeline` 请求
- 实现智能的兼容性匹配

### 兼容性矩阵

| 节点模式 | 可处理的请求模式 | 说明 |
|---------|-----------------|------|
| `sglang` | `sglang`, `vlm`, `pipeline`, `auto` | VLM节点，全兼容 |
| `vlm` | `sglang`, `vlm`, `pipeline`, `auto` | VLM节点，全兼容 |
| `vlm-sglang-client` | `sglang`, `vlm`, `pipeline`, `auto` | VLM节点，全兼容 |
| `vlm-transformers` | `vlm-transformers`, `pipeline`, `auto` | VLM节点，部分兼容 |
| `pipeline` | `pipeline`, `auto` | 传统节点，仅支持pipeline |
| `auto` | 所有模式 | 自动选择节点 |

## 🚀 使用方法

### 1. API 接口

#### 获取兼容性矩阵
```bash
GET /api/v1/compatibility/matrix
```

#### 检查兼容性
```bash
GET /api/v1/compatibility/check?node_mode=sglang&request_mode=pipeline
```

#### 查找兼容节点
```bash
GET /api/v1/compatibility/find-nodes?request_mode=pipeline&service_type=document
```

#### 模拟节点选择
```bash
POST /api/v1/compatibility/simulate-selection
{
  "request_mode": "pipeline",
  "service_type": "document",
  "file_type": "pdf"
}
```

### 2. 命令行工具

#### 显示兼容性矩阵
```bash
python tools/compatibility_tester.py show-compatibility
```

#### 查找兼容节点
```bash
python tools/compatibility_tester.py find-compatible-nodes --request-mode pipeline
```

#### 测试所有组合
```bash
python tools/compatibility_tester.py test-all-combinations
```

#### 模拟选择过程
```bash
python tools/compatibility_tester.py simulate-selection --request-mode pipeline --service-type document
```

#### 列出所有节点
```bash
python tools/compatibility_tester.py list-nodes
```

## 🔧 技术实现

### 1. 兼容性管理器

```python
from almond_parser.utils.parse_mode_compatibility import ParseModeCompatibility

# 检查兼容性
is_compatible = ParseModeCompatibility.can_handle_request("sglang", "pipeline")

# 获取最佳执行模式
execution_mode = ParseModeCompatibility.get_best_execution_mode("sglang", "pipeline")

# 查找兼容的节点模式
compatible_modes = ParseModeCompatibility.get_compatible_nodes_modes("pipeline")
```

### 2. 增强的节点选择器

```python
from almond_parser.utils.node_selector import NodeSelector

# 使用兼容性匹配
node = await node_selector.select_node(
    service_type="document",
    parse_mode="pipeline",
    use_compatibility=True  # 启用兼容性匹配
)

# 获取实际执行模式
execution_mode = getattr(node, '_execution_mode', 'pipeline')
```

### 3. 自动降级和重试

系统现在支持：
- **VLM 失败自动降级**：VLM 解析失败时自动切换到 pipeline 模式
- **节点故障重试**：节点不可用时自动安排重试
- **兼容性匹配**：智能选择兼容的节点

## 📊 优化效果

### 之前的问题
```
请求: pipeline 模式
节点: sglang 模式
结果: ❌ 无可用节点
```

### 优化后的效果
```
请求: pipeline 模式
节点: sglang 模式  
结果: ✅ 选择 sglang 节点，以 pipeline 模式执行
```

### 兼容性示例

| 场景 | 节点配置 | 用户请求 | 选择结果 | 执行模式 |
|------|---------|---------|---------|---------|
| 场景1 | sglang | pipeline | ✅ 选择 | pipeline |
| 场景2 | sglang | sglang | ✅ 选择 | sglang |
| 场景3 | pipeline | sglang | ❌ 不兼容 | - |
| 场景4 | pipeline | pipeline | ✅ 选择 | pipeline |
| 场景5 | auto | 任何模式 | ✅ 选择 | 请求模式 |

## 🛠️ 配置和管理

### 1. 节点配置建议

- **高性能场景**：配置为 `sglang` 模式，支持所有请求
- **兼容性场景**：配置为 `auto` 模式，自动适配
- **资源受限**：配置为 `pipeline` 模式，仅支持传统解析

### 2. 监控和调试

#### 查看节点状态
```bash
python tools/compatibility_tester.py list-nodes
```

#### 测试特定组合
```bash
python tools/compatibility_tester.py simulate-selection --request-mode pipeline
```

#### 查看兼容性矩阵
```bash
python tools/compatibility_tester.py show-compatibility
```

## 🔍 故障排除

### 常见问题

#### 1. 仍然提示"无可用节点"
**可能原因**：
- 所有节点都离线
- 所有节点都达到最大并发数
- 请求模式确实没有兼容的节点

**排查方法**：
```bash
# 检查节点状态
python tools/compatibility_tester.py list-nodes

# 模拟选择过程
python tools/compatibility_tester.py simulate-selection --request-mode YOUR_MODE
```

#### 2. 兼容性匹配不生效
**可能原因**：
- 使用了旧的选择方法
- 兼容性匹配被禁用

**解决方法**：
```python
# 确保使用新的选择方法
node = await node_selector.select_node(
    service_type="document",
    parse_mode="pipeline",
    use_compatibility=True  # 必须设置为 True
)
```

#### 3. 执行模式不正确
**检查方法**：
```python
# 检查节点是否包含执行模式信息
execution_mode = getattr(node, '_execution_mode', None)
if not execution_mode:
    # 使用请求模式作为默认值
    execution_mode = request_mode
```

## 📈 性能优化

### 1. 选择策略优化
- 按节点模式优先级排序
- 优先选择负载较低的节点
- 考虑节点的历史成功率

### 2. 缓存机制
- 兼容性检查结果缓存
- 节点状态缓存
- 减少数据库查询

### 3. 监控指标
- 兼容性匹配成功率
- 节点利用率
- 请求响应时间

## 🔄 升级指南

### 从旧版本升级

1. **更新代码**：
   ```bash
   # 拉取最新代码
   git pull origin main
   ```

2. **运行数据库迁移**：
   ```bash
   python migrations/add_retry_fields.py migrate
   ```

3. **重启服务**：
   ```bash
   # 重启 Web 服务
   python -m almond_parser.main

   # 重启 ARQ 工作器
   python -m almond_parser.worker
   ```

4. **验证功能**：
   ```bash
   # 测试兼容性
   python tools/compatibility_tester.py test-all-combinations
   ```

### 配置迁移

旧的节点选择调用：
```python
# 旧方式
node = await node_selector.select_best_node("document", "pipeline")
```

新的兼容性选择调用：
```python
# 新方式
node = await node_selector.select_node(
    service_type="document",
    parse_mode="pipeline",
    use_compatibility=True
)
```

## 📝 总结

通过引入解析模式兼容性机制，系统现在能够：

1. **智能匹配**：VLM 节点可以处理 pipeline 请求
2. **资源优化**：充分利用可用的节点资源
3. **向下兼容**：保持对现有配置的支持
4. **易于管理**：提供完整的工具和 API 支持

这个优化解决了你提到的核心问题：当只有一个 sglang 模式的节点时，pipeline 请求也能被正确处理，而不是报告"无可用节点"的错误。
