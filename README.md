# 🎯 AI中心解析流 (AiCenter ParserFlow) - 统一文档解析平台

[![Python](https://img.shields.io/badge/Python-3.10+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![uv](https://img.shields.io/badge/uv-latest-purple.svg)](https://docs.astral.sh/uv/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Status](https://img.shields.io/badge/Status-Production-brightgreen.svg)]()

> 基于 MinerU 的企业级智能文档解析平台，支持多格式文档解析、分布式处理、智能重试和资源保护。支持选择性部署和统一管理。

## 📋 项目概述

杏仁解析是一个完整的文档解析生态系统，包含三个核心组件：

- **🧠 almond_parser**: 核心解析服务，提供任务管理、节点调度、智能重试
- **⚡ mineru-api**: MinerU API 封装，支持多种解析模式和GPU加速
- **🌐 web**: 管理后台界面，提供系统监控、任务管理、配置管理

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web 管理后台   │    │  Almond Parser  │    │   MinerU API    │
│                │    │   核心服务      │    │   解析引擎      │
│ • 系统监控      │◄──►│ • 任务调度      │◄──►│ • VLM 解析      │
│ • 任务管理      │    │ • 节点管理      │    │ • Pipeline 解析  │
│ • 配置管理      │    │ • 智能重试      │    │ • GPU 加速      │
│ • 用户管理      │    │ • 资源保护      │    │ • 多格式支持    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   MySQL 数据库   │
                    │ • 任务状态      │
                    │ • 节点信息      │
                    │ • 用户数据      │
                    │ • 解析结果      │
                    └─────────────────┘
```

## ✨ 核心特性

### 🎯 智能解析
- **多格式支持**: PDF、Word、PowerPoint、图像等
- **双模式解析**: VLM (视觉语言模型) + Pipeline (传统OCR)
- **自动降级**: VLM失败时自动切换到Pipeline模式
- **质量检查**: 智能验证解析结果质量

### 🛡️ 资源保护
- **智能节点分配**: CPU节点优先处理Pipeline请求
- **GPU资源保护**: VLM请求直接使用GPU节点
- **负载均衡**: 基于节点负载的智能调度
- **容错机制**: 节点故障自动切换

### 🔄 智能重试
- **自动重试**: 节点不可用时定时重试
- **指数退避**: 避免重试风暴
- **降级重试**: VLM失败时自动降级到Pipeline
- **状态追踪**: 完整的重试状态管理

### 📊 企业级特性
- **API认证**: 基于Token的安全认证
- **批量处理**: 支持批量文档上传和处理
- **实时监控**: 系统状态和任务进度实时监控
- **详细日志**: 完整的操作日志和错误追踪

## 🚀 快速开始

### 📦 一键安装

本项目支持基于 `uv` 的选择性安装，可以根据需要只安装特定服务：

```bash
# 1. 克隆项目
git clone https://gitlab.com/aicenter/parserflow.git
cd aicenter-parserflow

# 2. 选择安装方式

# 仅安装 MinerU API 服务
python bootstrap.py --service mineru-api

# 仅安装杏仁解析服务
python bootstrap.py --service almond-parser

# 安装所有服务
python bootstrap.py --service all

# 开发环境（包含所有服务+开发工具）
python bootstrap.py --service all --dev
```

### 🎛️ 直接使用 uv

如果你已经安装了 `uv`，也可以直接使用：

```bash
# 仅安装 MinerU API + 平台依赖
uv sync --extra mineru-api --extra windows  # Windows
uv sync --extra mineru-api --extra linux    # Linux

# 仅安装杏仁解析服务 + 平台依赖
uv sync --extra almond-parser --extra windows  # Windows
uv sync --extra almond-parser --extra linux    # Linux

# 安装所有服务
uv sync --extra all --extra windows  # Windows
uv sync --extra all --extra linux    # Linux
```

### 🚀 启动服务

```bash
# 自动启动（根据安装环境）
python start_services.py

# 指定启动服务
python start_services.py --service mineru-api
python start_services.py --service almond-parser

# 启动所有可用服务
python start_services.py --all

# 自定义端口
python start_services.py --mineru-port 8000 --almond-port 8001
```

### 📋 环境要求

- Python 3.10+
- MySQL 8.0+ (仅杏仁解析服务需要)
- Redis 6.0+
- CUDA 11.8+ (GPU加速，可选)

### 一键部署

```bash
# 克隆项目
git clone https://gitlab.bdo.com.cn/lxai/aicenter-parserflow.git
cd aicenter-parserflow

# 使用 uv 快速部署
./bootstrap.sh
```

### 分步部署

#### 1. 环境准备

```bash
# 安装 uv (推荐)
curl -LsSf https://astral.sh/uv/install.sh | sh

# 或使用 pip
pip install uv
```

#### 2. 核心服务部署

```bash
# 部署 almond_parser 核心服务
cd almond_parser
uv sync
uv run python main.py

# 部署 mineru-api 解析引擎
cd ../mineru-api
uv sync
uv run python main.py

# 部署 web 管理后台
cd ../web
uv sync
uv run python main.py
```

#### 3. 数据库初始化

```bash
# 初始化数据库
cd almond_parser
python migrate.py init-db
```

### 配置说明

主要配置文件：
- `almond_parser/config.py` - 核心服务配置
- `mineru-api/config.py` - 解析引擎配置
- `web/config.py` - Web界面配置

关键配置项：
```python
# 数据库配置
MYSQL_HOST = "localhost"
MYSQL_PORT = 3306
MYSQL_DATABASE = "almond_parser"

# Redis配置
REDIS_HOST = "127.0.0.1"
REDIS_PORT = 6379

# GPU配置 (可选)
CUDA_VISIBLE_DEVICES = "0,1"
```

## 📖 使用指南

### API 使用

#### 1. 获取API密钥

```bash
# 通过Web管理后台获取，或使用CLI
curl -X POST "http://localhost:8010/api/v1/auth/token" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "password"}'
```

#### 2. 上传文档解析

```bash
curl -X POST "http://localhost:8010/api/v1/document/upload" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -F "file=@document.pdf" \
  -F "parse_mode=auto"
```

#### 3. 查询解析结果

```bash
curl -X GET "http://localhost:8010/api/v1/document/{document_id}/result" \
  -H "Authorization: Bearer YOUR_API_KEY"
```

### Web 管理界面

访问 `http://localhost:3000` 进入管理后台：

- **📊 系统概览**: 实时监控系统状态和性能指标
- **📄 任务管理**: 查看和管理解析任务
- **🖥️ 节点管理**: 管理MinerU解析节点
- **👥 用户管理**: 管理用户和API密钥
- **⚙️ 系统配置**: 调整系统参数和策略

## 🔧 高级配置

### 节点配置

#### CPU节点 (Pipeline模式)
```python
{
    "name": "cpu-node-1",
    "parse_mode": "pipeline",
    "service_type": "document",
    "max_concurrent_tasks": 3
}
```

#### GPU节点 (VLM模式)
```python
{
    "name": "gpu-node-1",
    "parse_mode": "sglang",
    "service_type": "universal",
    "max_concurrent_tasks": 5,
    "gpu_memory": "24GB"
}
```

### 解析策略配置

```python
# 资源保护策略
RESOURCE_PROTECTION = {
    "pipeline_priority": True,  # Pipeline请求优先使用CPU节点
    "gpu_protection": True,     # 保护GPU资源
    "auto_fallback": True       # 自动降级重试
}

# 重试策略
RETRY_STRATEGY = {
    "max_retries": 3,
    "base_delay": 300,          # 5分钟
    "max_delay": 3600,          # 1小时
    "exponential_backoff": True
}
```

## 📊 监控和运维

### 系统监控

- **实时指标**: 任务队列、节点状态、系统负载
- **性能监控**: 解析速度、成功率、错误统计
- **资源监控**: CPU、内存、GPU使用率
- **日志监控**: 错误日志、操作日志、性能日志

### 数据库管理

```bash
# 查看迁移状态
python migrate.py status

# 创建新迁移
python migrate.py revision -m "描述变更"

# 应用迁移
python migrate.py upgrade

# 自动迁移
python migrate.py auto-migrate
```

### 故障排除

常见问题和解决方案：

1. **节点连接失败**
   ```bash
   # 检查节点状态
   curl http://node-url/health
   ```

2. **任务堆积**
   ```bash
   # 检查队列状态
   python -c "from almond_parser.tasks.arq_app import arq_manager; print(arq_manager.get_queue_info())"
   ```

3. **GPU内存不足**
   ```bash
   # 调整并发数
   # 在节点配置中降低 max_concurrent_tasks
   ```

## 🔄 版本更新

### 更新流程

```bash
# 1. 备份数据库
mysqldump -u root -p almond_parser > backup_$(date +%Y%m%d).sql

# 2. 拉取最新代码
git pull origin main

# 3. 更新依赖
uv sync

# 4. 应用数据库迁移
python migrate.py upgrade

# 5. 重启服务
systemctl restart almond-parser
systemctl restart mineru-api
systemctl restart almond-web
```

## 🤝 开发指南

### 开发环境搭建

```bash
# 克隆项目
git clone https://gitlab.bdo.com.cn/lxai/aicenter-parserflow.git
cd aicenter-parserflow

# 安装开发依赖
uv sync --dev

# 运行测试
uv run pytest

# 代码格式化
uv run black .
uv run isort .
```

### 项目结构

```
aicenter-parserflow/
├── almond_parser/          # 核心解析服务
│   ├── api/               # API路由
│   ├── db/                # 数据库模型
│   ├── services/          # 业务逻辑
│   ├── tasks/             # 异步任务
│   ├── utils/             # 工具函数
│   └── main.py            # 服务入口
├── mineru-api/            # MinerU API封装
│   ├── app/               # FastAPI应用
│   ├── models/            # 数据模型
│   ├── services/          # 解析服务
│   └── main.py            # 服务入口
├── web/                   # Web管理界面
│   ├── components/        # React组件
│   ├── pages/             # 页面组件
│   ├── services/          # API服务
│   └── main.py            # 服务入口
└── docs/                  # 项目文档
```

### 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [MinerU](https://github.com/opendatalab/MinerU) - 强大的文档解析引擎
- [FastAPI](https://fastapi.tiangolo.com/) - 现代化的Python Web框架
- [SQLAlchemy](https://sqlalchemy.org/) - Python SQL工具包
- [ARQ](https://arq-docs.helpmanual.io/) - 异步任务队列

## 📞 支持

- 📧 邮箱: <EMAIL>
- 💬 Issues: [GitLab Issues](https://gitlab.bdo.com.cn/lxai/aicenter-parserflow/-/issues)
- 📖 文档: [项目文档](docs/)

---

<div align="center">
  <strong>🌰 杏仁解析 - 让文档解析更智能、更可靠</strong>
</div>
