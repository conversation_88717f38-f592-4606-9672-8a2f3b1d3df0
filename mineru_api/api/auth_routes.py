# -*- encoding: utf-8 -*-
"""
认证管理API路由
"""

from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, status
from loguru import logger

from mineru_api.auth.models import CreateAPIKeyRequest, APIKeyInfo, AuthResult
from mineru_api.auth.manager import get_auth_manager
from mineru_api.auth.middleware import create_auth_dependency
from mineru_api.config import ENABLE_AUTH

# 创建认证管理路由
auth_router = APIRouter(prefix="/auth", tags=["认证管理"])

# 管理员认证依赖（可以使用特殊的管理员API key或其他方式）
admin_auth_dependency = None
if ENABLE_AUTH:
    try:
        auth_manager = get_auth_manager()
        admin_auth_dependency = create_auth_dependency(auth_manager)
    except RuntimeError:
        pass


@auth_router.post("/keys", response_model=dict)
async def create_api_key(
    request: CreateAPIKeyRequest,
    auth_result: AuthResult = Depends(admin_auth_dependency) if ENABLE_AUTH and admin_auth_dependency else None
):
    """创建API key"""
    if not ENABLE_AUTH:
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="认证功能未启用"
        )
    
    try:
        auth_manager = get_auth_manager()
        api_key = await auth_manager.create_api_key(request)
        
        return {
            "message": "API key创建成功",
            "api_key": api_key.key,
            "name": api_key.name,
            "expires_at": api_key.expires_at.isoformat() if api_key.expires_at else None,
            "rate_limit": api_key.rate_limit
        }
        
    except Exception as e:
        logger.error(f"创建API key失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建API key失败: {str(e)}"
        )


@auth_router.get("/keys", response_model=List[APIKeyInfo])
async def list_api_keys(
    include_revoked: bool = False,
    auth_result: AuthResult = Depends(admin_auth_dependency) if ENABLE_AUTH and admin_auth_dependency else None
):
    """列出API keys"""
    if not ENABLE_AUTH:
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="认证功能未启用"
        )
    
    try:
        auth_manager = get_auth_manager()
        api_keys = await auth_manager.list_api_keys(include_revoked=include_revoked)
        return api_keys
        
    except Exception as e:
        logger.error(f"列出API keys失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"列出API keys失败: {str(e)}"
        )


@auth_router.delete("/keys/{api_key}")
async def revoke_api_key(
    api_key: str,
    auth_result: AuthResult = Depends(admin_auth_dependency) if ENABLE_AUTH and admin_auth_dependency else None
):
    """撤销API key"""
    if not ENABLE_AUTH:
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="认证功能未启用"
        )
    
    try:
        auth_manager = get_auth_manager()
        success = await auth_manager.revoke_api_key(api_key)
        
        if success:
            return {"message": "API key已撤销"}
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API key不存在"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"撤销API key失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"撤销API key失败: {str(e)}"
        )


@auth_router.get("/statistics")
async def get_auth_statistics(
    auth_result: AuthResult = Depends(admin_auth_dependency) if ENABLE_AUTH and admin_auth_dependency else None
):
    """获取认证统计信息"""
    if not ENABLE_AUTH:
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="认证功能未启用"
        )
    
    try:
        auth_manager = get_auth_manager()
        stats = await auth_manager.get_statistics()
        return stats
        
    except Exception as e:
        logger.error(f"获取认证统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取认证统计失败: {str(e)}"
        )


@auth_router.post("/cleanup")
async def cleanup_expired_keys(
    auth_result: AuthResult = Depends(admin_auth_dependency) if ENABLE_AUTH and admin_auth_dependency else None
):
    """清理过期的API keys"""
    if not ENABLE_AUTH:
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="认证功能未启用"
        )
    
    try:
        auth_manager = get_auth_manager()
        cleaned_count = await auth_manager.cleanup_expired_keys()
        
        return {
            "message": f"已清理 {cleaned_count} 个过期的API key",
            "cleaned_count": cleaned_count
        }
        
    except Exception as e:
        logger.error(f"清理过期API keys失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清理过期API keys失败: {str(e)}"
        )


# 验证API key的公开接口（用于远程认证后端）
@auth_router.post("/verify")
async def verify_api_key_endpoint(request: dict):
    """验证API key（用于远程认证后端）"""
    if not ENABLE_AUTH:
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="认证功能未启用"
        )
    
    api_key = request.get("api_key")
    client_ip = request.get("client_ip")
    
    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="缺少api_key参数"
        )
    
    try:
        auth_manager = get_auth_manager()
        api_key_obj = await auth_manager.verify_api_key(api_key, client_ip)
        
        if api_key_obj:
            return {
                "success": True,
                "api_key": api_key_obj.dict()
            }
        else:
            return {
                "success": False,
                "error": "无效的API key"
            }
            
    except Exception as e:
        logger.error(f"验证API key失败: {e}")
        return {
            "success": False,
            "error": f"验证失败: {str(e)}"
        }
