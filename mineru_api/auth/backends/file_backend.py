# -*- encoding: utf-8 -*-
"""
文件认证后端 - 使用JSON文件存储API key
"""

import json
import secrets
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, List, Dict, Any
from loguru import logger

from .base import AuthBackend
from ..models import APIKey, APIKeyStatus, CreateAPIKeyRequest


class FileAuthBackend(AuthBackend):
    """文件认证后端"""
    
    def __init__(self, config):
        super().__init__(config)
        self.file_path = Path(config.file_path)
        self._ensure_file_exists()
    
    def _ensure_file_exists(self):
        """确保文件存在"""
        if not self.file_path.exists():
            self.file_path.parent.mkdir(parents=True, exist_ok=True)
            self._save_keys({})
    
    def _load_keys(self) -> Dict[str, Dict[str, Any]]:
        """加载API keys"""
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载API keys失败: {e}")
            return {}
    
    def _save_keys(self, keys: Dict[str, Dict[str, Any]]):
        """保存API keys"""
        try:
            with open(self.file_path, 'w', encoding='utf-8') as f:
                json.dump(keys, f, ensure_ascii=False, indent=2, default=str)
        except Exception as e:
            logger.error(f"保存API keys失败: {e}")
            raise
    
    def _dict_to_api_key(self, key: str, data: Dict[str, Any]) -> APIKey:
        """将字典转换为APIKey对象"""
        return APIKey(
            key=key,
            name=data.get('name', ''),
            status=APIKeyStatus(data.get('status', APIKeyStatus.ACTIVE)),
            created_at=datetime.fromisoformat(data['created_at']) if isinstance(data.get('created_at'), str) else data.get('created_at', datetime.now()),
            expires_at=datetime.fromisoformat(data['expires_at']) if data.get('expires_at') and isinstance(data['expires_at'], str) else data.get('expires_at'),
            last_used_at=datetime.fromisoformat(data['last_used_at']) if data.get('last_used_at') and isinstance(data['last_used_at'], str) else data.get('last_used_at'),
            usage_count=data.get('usage_count', 0),
            rate_limit=data.get('rate_limit'),
            allowed_ips=data.get('allowed_ips'),
            metadata=data.get('metadata', {})
        )
    
    def _api_key_to_dict(self, api_key: APIKey) -> Dict[str, Any]:
        """将APIKey对象转换为字典"""
        return {
            'name': api_key.name,
            'status': api_key.status.value,
            'created_at': api_key.created_at.isoformat(),
            'expires_at': api_key.expires_at.isoformat() if api_key.expires_at else None,
            'last_used_at': api_key.last_used_at.isoformat() if api_key.last_used_at else None,
            'usage_count': api_key.usage_count,
            'rate_limit': api_key.rate_limit,
            'allowed_ips': api_key.allowed_ips,
            'metadata': api_key.metadata
        }
    
    async def verify_api_key(self, api_key: str, client_ip: Optional[str] = None) -> Optional[APIKey]:
        """验证API key"""
        keys = self._load_keys()
        
        if api_key not in keys:
            return None
        
        api_key_obj = self._dict_to_api_key(api_key, keys[api_key])
        
        # 检查有效性
        if not api_key_obj.is_valid():
            return None
        
        # 检查IP限制
        if client_ip and not api_key_obj.is_ip_allowed(client_ip):
            return None
        
        return api_key_obj
    
    async def create_api_key(self, request: CreateAPIKeyRequest) -> APIKey:
        """创建API key"""
        # 生成API key
        api_key = f"mk_{secrets.token_urlsafe(32)}"
        
        # 计算过期时间
        expires_at = None
        if request.expires_days:
            expires_at = datetime.now() + timedelta(days=request.expires_days)
        
        # 创建API key对象
        api_key_obj = APIKey(
            key=api_key,
            name=request.name,
            status=APIKeyStatus.ACTIVE,
            expires_at=expires_at,
            rate_limit=request.rate_limit,
            allowed_ips=request.allowed_ips,
            metadata=request.metadata
        )
        
        # 保存到文件
        keys = self._load_keys()
        keys[api_key] = self._api_key_to_dict(api_key_obj)
        self._save_keys(keys)
        
        logger.info(f"创建API key: {request.name} ({api_key[:10]}...)")
        return api_key_obj
    
    async def revoke_api_key(self, api_key: str) -> bool:
        """撤销API key"""
        keys = self._load_keys()
        
        if api_key not in keys:
            return False
        
        keys[api_key]['status'] = APIKeyStatus.REVOKED.value
        self._save_keys(keys)
        
        logger.info(f"撤销API key: {api_key[:10]}...")
        return True
    
    async def list_api_keys(self) -> List[APIKey]:
        """列出所有API key"""
        keys = self._load_keys()
        return [self._dict_to_api_key(key, data) for key, data in keys.items()]
    
    async def update_usage(self, api_key: str) -> None:
        """更新使用统计"""
        keys = self._load_keys()
        
        if api_key in keys:
            keys[api_key]['usage_count'] = keys[api_key].get('usage_count', 0) + 1
            keys[api_key]['last_used_at'] = datetime.now().isoformat()
            self._save_keys(keys)
    
    async def get_api_key_info(self, api_key: str) -> Optional[APIKey]:
        """获取API key详细信息"""
        keys = self._load_keys()
        
        if api_key not in keys:
            return None
        
        return self._dict_to_api_key(api_key, keys[api_key])
    
    async def cleanup_expired_keys(self) -> int:
        """清理过期的API key"""
        keys = self._load_keys()
        now = datetime.now()
        expired_count = 0
        
        for key, data in list(keys.items()):
            expires_at = data.get('expires_at')
            if expires_at:
                if isinstance(expires_at, str):
                    expires_at = datetime.fromisoformat(expires_at)
                
                if now > expires_at:
                    keys[key]['status'] = APIKeyStatus.EXPIRED.value
                    expired_count += 1
        
        if expired_count > 0:
            self._save_keys(keys)
            logger.info(f"标记 {expired_count} 个过期的API key")
        
        return expired_count
