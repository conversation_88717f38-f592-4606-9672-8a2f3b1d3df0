# -*- encoding: utf-8 -*-
"""
SQLite认证后端 - 使用SQLite数据库存储API key
"""

import sqlite3
import secrets
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, List
from loguru import logger

from .base import AuthBackend
from ..models import <PERSON>Key, APIKeyStatus, CreateAPIKeyRequest


class SQLiteAuthBackend(AuthBackend):
    """SQLite认证后端"""
    
    def __init__(self, config):
        super().__init__(config)
        self.db_path = Path(config.sqlite_path)
        self._init_database()
    
    def _init_database(self):
        """初始化数据库"""
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS api_keys (
                    key TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    status TEXT NOT NULL DEFAULT 'active',
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP,
                    last_used_at TIMESTAMP,
                    usage_count INTEGER NOT NULL DEFAULT 0,
                    rate_limit INTEGER,
                    allowed_ips TEXT,
                    metadata TEXT
                )
            """)
            
            # 创建索引
            conn.execute("CREATE INDEX IF NOT EXISTS idx_api_keys_status ON api_keys(status)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_api_keys_expires_at ON api_keys(expires_at)")
            
            conn.commit()
    
    def _row_to_api_key(self, row) -> APIKey:
        """将数据库行转换为APIKey对象"""
        return APIKey(
            key=row[0],
            name=row[1],
            status=APIKeyStatus(row[2]),
            created_at=datetime.fromisoformat(row[3]) if isinstance(row[3], str) else row[3],
            expires_at=datetime.fromisoformat(row[4]) if row[4] and isinstance(row[4], str) else row[4],
            last_used_at=datetime.fromisoformat(row[5]) if row[5] and isinstance(row[5], str) else row[5],
            usage_count=row[6] or 0,
            rate_limit=row[7],
            allowed_ips=json.loads(row[8]) if row[8] else None,
            metadata=json.loads(row[9]) if row[9] else {}
        )
    
    async def verify_api_key(self, api_key: str, client_ip: Optional[str] = None) -> Optional[APIKey]:
        """验证API key"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT * FROM api_keys WHERE key = ? AND status = 'active'",
                    (api_key,)
                )
                row = cursor.fetchone()
                
                if not row:
                    return None
                
                api_key_obj = self._row_to_api_key(row)
                
                # 检查有效性
                if not api_key_obj.is_valid():
                    return None
                
                # 检查IP限制
                if client_ip and not api_key_obj.is_ip_allowed(client_ip):
                    return None
                
                return api_key_obj
                
        except Exception as e:
            logger.error(f"验证API key失败: {e}")
            return None
    
    async def create_api_key(self, request: CreateAPIKeyRequest) -> APIKey:
        """创建API key"""
        # 生成API key
        api_key = f"mk_{secrets.token_urlsafe(32)}"
        
        # 计算过期时间
        expires_at = None
        if request.expires_days:
            expires_at = datetime.now() + timedelta(days=request.expires_days)
        
        # 创建API key对象
        api_key_obj = APIKey(
            key=api_key,
            name=request.name,
            status=APIKeyStatus.ACTIVE,
            expires_at=expires_at,
            rate_limit=request.rate_limit,
            allowed_ips=request.allowed_ips,
            metadata=request.metadata
        )
        
        # 保存到数据库
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO api_keys 
                    (key, name, status, created_at, expires_at, rate_limit, allowed_ips, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    api_key_obj.key,
                    api_key_obj.name,
                    api_key_obj.status.value,
                    api_key_obj.created_at.isoformat(),
                    api_key_obj.expires_at.isoformat() if api_key_obj.expires_at else None,
                    api_key_obj.rate_limit,
                    json.dumps(api_key_obj.allowed_ips) if api_key_obj.allowed_ips else None,
                    json.dumps(api_key_obj.metadata) if api_key_obj.metadata else None
                ))
                conn.commit()
                
            logger.info(f"创建API key: {request.name} ({api_key[:10]}...)")
            return api_key_obj
            
        except Exception as e:
            logger.error(f"创建API key失败: {e}")
            raise
    
    async def revoke_api_key(self, api_key: str) -> bool:
        """撤销API key"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "UPDATE api_keys SET status = 'revoked' WHERE key = ?",
                    (api_key,)
                )
                conn.commit()
                
                if cursor.rowcount > 0:
                    logger.info(f"撤销API key: {api_key[:10]}...")
                    return True
                return False
                
        except Exception as e:
            logger.error(f"撤销API key失败: {e}")
            return False
    
    async def list_api_keys(self) -> List[APIKey]:
        """列出所有API key"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("SELECT * FROM api_keys ORDER BY created_at DESC")
                rows = cursor.fetchall()
                
                return [self._row_to_api_key(row) for row in rows]
                
        except Exception as e:
            logger.error(f"列出API keys失败: {e}")
            return []
    
    async def update_usage(self, api_key: str) -> None:
        """更新使用统计"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    UPDATE api_keys 
                    SET usage_count = usage_count + 1, last_used_at = ?
                    WHERE key = ?
                """, (datetime.now().isoformat(), api_key))
                conn.commit()
                
        except Exception as e:
            logger.error(f"更新使用统计失败: {e}")
    
    async def get_api_key_info(self, api_key: str) -> Optional[APIKey]:
        """获取API key详细信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("SELECT * FROM api_keys WHERE key = ?", (api_key,))
                row = cursor.fetchone()
                
                if row:
                    return self._row_to_api_key(row)
                return None
                
        except Exception as e:
            logger.error(f"获取API key信息失败: {e}")
            return None
    
    async def cleanup_expired_keys(self) -> int:
        """清理过期的API key"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    UPDATE api_keys 
                    SET status = 'expired' 
                    WHERE expires_at < ? AND status = 'active'
                """, (datetime.now().isoformat(),))
                conn.commit()
                
                expired_count = cursor.rowcount
                if expired_count > 0:
                    logger.info(f"标记 {expired_count} 个过期的API key")
                
                return expired_count
                
        except Exception as e:
            logger.error(f"清理过期API keys失败: {e}")
            return 0
