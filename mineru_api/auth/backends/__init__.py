# -*- encoding: utf-8 -*-
"""
认证后端模块
"""

from typing import Optional
from ..models import AuthConfig, AuthBackendType
from .base import AuthBackend
from .file_backend import FileAuthBackend
from .sqlite_backend import SQLiteAuthBackend
from .remote_backend import RemoteAuthBackend


def get_auth_backend(config: AuthConfig) -> AuthBackend:
    """根据配置获取认证后端"""
    if config.backend_type == AuthBackendType.FILE:
        return FileAuthBackend(config)
    elif config.backend_type == AuthBackendType.SQLITE:
        return SQLiteAuthBackend(config)
    elif config.backend_type == AuthBackendType.REMOTE:
        return RemoteAuthBackend(config)
    else:
        raise ValueError(f"不支持的认证后端类型: {config.backend_type}")


__all__ = [
    'AuthBackend',
    'FileAuthBackend',
    'SQLiteAuthBackend', 
    'RemoteAuthBackend',
    'get_auth_backend'
]
