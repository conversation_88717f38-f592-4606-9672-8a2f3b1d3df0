# -*- encoding: utf-8 -*-
"""
认证后端基类
"""

from abc import ABC, abstractmethod
from typing import Optional, List
from ..models import APIKey, AuthConfig, CreateAPIKeyRequest


class AuthBackend(ABC):
    """认证后端基类"""
    
    def __init__(self, config: AuthConfig):
        self.config = config
    
    @abstractmethod
    async def verify_api_key(self, api_key: str, client_ip: Optional[str] = None) -> Optional[APIKey]:
        """验证API key"""
        pass
    
    @abstractmethod
    async def create_api_key(self, request: CreateAPIKeyRequest) -> APIKey:
        """创建API key"""
        pass
    
    @abstractmethod
    async def revoke_api_key(self, api_key: str) -> bool:
        """撤销API key"""
        pass
    
    @abstractmethod
    async def list_api_keys(self) -> List[APIKey]:
        """列出所有API key"""
        pass
    
    @abstractmethod
    async def update_usage(self, api_key: str) -> None:
        """更新使用统计"""
        pass
    
    @abstractmethod
    async def get_api_key_info(self, api_key: str) -> Optional[APIKey]:
        """获取API key详细信息"""
        pass
    
    async def cleanup_expired_keys(self) -> int:
        """清理过期的API key（可选实现）"""
        return 0
