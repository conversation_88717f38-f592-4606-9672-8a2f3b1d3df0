# -*- encoding: utf-8 -*-
"""
认证系统启动模块
"""

import asyncio
from loguru import logger
from .manager import get_auth_manager


async def start_auth_cleanup_tasks():
    """启动认证系统的后台任务"""
    try:
        auth_manager = get_auth_manager()

        # 手动启动清理任务
        auth_manager._start_cleanup_task()

        logger.info("✅ 认证系统后台任务已启动")

    except RuntimeError:
        logger.debug("认证管理器未初始化，跳过后台任务启动")
    except Exception as e:
        logger.error(f"❌ 启动认证后台任务失败: {e}")
        # 不抛出异常，避免影响应用启动


def setup_auth_startup_handler(app):
    """设置认证系统启动处理器"""
    
    @app.on_event("startup")
    async def startup_auth_tasks():
        """应用启动时启动认证后台任务"""
        await start_auth_cleanup_tasks()
    
    return app
