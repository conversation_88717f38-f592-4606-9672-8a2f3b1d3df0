# -*- encoding: utf-8 -*-
"""
认证相关数据模型
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from enum import Enum


class AuthBackendType(str, Enum):
    """认证后端类型"""
    FILE = "file"
    SQLITE = "sqlite"
    REMOTE = "remote"


class APIKeyStatus(str, Enum):
    """API Key状态"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    EXPIRED = "expired"
    REVOKED = "revoked"


class APIKey(BaseModel):
    """API Key模型"""
    key: str = Field(..., description="API密钥")
    name: str = Field(..., description="密钥名称")
    status: APIKeyStatus = Field(default=APIKeyStatus.ACTIVE, description="状态")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    expires_at: Optional[datetime] = Field(None, description="过期时间")
    last_used_at: Optional[datetime] = Field(None, description="最后使用时间")
    usage_count: int = Field(default=0, description="使用次数")
    rate_limit: Optional[int] = Field(None, description="速率限制(每分钟)")
    allowed_ips: Optional[List[str]] = Field(None, description="允许的IP地址")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    
    def is_valid(self) -> bool:
        """检查API key是否有效"""
        if self.status != APIKeyStatus.ACTIVE:
            return False
        
        if self.expires_at and datetime.now() > self.expires_at:
            return False
            
        return True
    
    def is_ip_allowed(self, ip: str) -> bool:
        """检查IP是否被允许"""
        if not self.allowed_ips:
            return True
        return ip in self.allowed_ips


class AuthConfig(BaseModel):
    """认证配置"""
    backend_type: AuthBackendType = Field(default=AuthBackendType.FILE, description="后端类型")
    
    # 文件后端配置
    file_path: str = Field(default="api_keys.json", description="API key文件路径")
    
    # SQLite后端配置
    sqlite_path: str = Field(default="auth.db", description="SQLite数据库路径")
    
    # 远程后端配置
    remote_url: Optional[str] = Field(None, description="远程认证服务URL")
    remote_timeout: int = Field(default=10, description="远程请求超时时间")
    remote_api_key: Optional[str] = Field(None, description="远程服务API key")
    
    # 通用配置
    enable_rate_limit: bool = Field(default=True, description="启用速率限制")
    default_rate_limit: int = Field(default=100, description="默认速率限制")
    enable_ip_whitelist: bool = Field(default=False, description="启用IP白名单")
    cache_ttl: int = Field(default=300, description="缓存TTL(秒)")


class AuthResult(BaseModel):
    """认证结果"""
    success: bool = Field(..., description="认证是否成功")
    api_key: Optional[APIKey] = Field(None, description="API key信息")
    error: Optional[str] = Field(None, description="错误信息")
    rate_limit_remaining: Optional[int] = Field(None, description="剩余请求次数")


class CreateAPIKeyRequest(BaseModel):
    """创建API key请求"""
    name: str = Field(..., description="密钥名称")
    expires_days: Optional[int] = Field(None, description="过期天数")
    rate_limit: Optional[int] = Field(None, description="速率限制")
    allowed_ips: Optional[List[str]] = Field(None, description="允许的IP地址")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


class APIKeyInfo(BaseModel):
    """API Key信息（不包含完整key）"""
    key_prefix: str = Field(..., description="密钥前缀")
    name: str = Field(..., description="密钥名称")
    status: APIKeyStatus = Field(..., description="状态")
    created_at: datetime = Field(..., description="创建时间")
    expires_at: Optional[datetime] = Field(None, description="过期时间")
    last_used_at: Optional[datetime] = Field(None, description="最后使用时间")
    usage_count: int = Field(..., description="使用次数")
    rate_limit: Optional[int] = Field(None, description="速率限制")
    allowed_ips: Optional[List[str]] = Field(None, description="允许的IP地址")
    metadata: Dict[str, Any] = Field(..., description="元数据")
