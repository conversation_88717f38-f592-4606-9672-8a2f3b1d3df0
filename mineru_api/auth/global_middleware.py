# -*- encoding: utf-8 -*-
"""
全局认证中间件 - 保护所有API接口包括LitServer的/predict
"""

import time
from typing import Set
from fastapi import Request, Response, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from loguru import logger

from .middleware import AuthMiddleware
from .manager import get_auth_manager
from ..config import ENABLE_AUTH


class GlobalAuthMiddleware(BaseHTTPMiddleware):
    """全局认证中间件"""
    
    def __init__(self, app, auth_middleware: AuthMiddleware = None):
        super().__init__(app)
        self.auth_middleware = auth_middleware
        
        # 不需要认证的路径
        self.public_paths: Set[str] = {
            "/",
            "/health",
            "/docs",
            "/openapi.json",
            "/redoc",
            "/favicon.ico"
        }
        
        # 不需要认证的路径前缀
        self.public_prefixes: Set[str] = {
            "/docs",
            "/redoc", 
            "/static",
            "/_internal"  # LitServer内部路径
        }
    
    def is_public_path(self, path: str) -> bool:
        """检查是否是公开路径"""
        # 检查完全匹配
        if path in self.public_paths:
            return True
        
        # 检查前缀匹配
        for prefix in self.public_prefixes:
            if path.startswith(prefix):
                return True
        
        return False
    
    async def dispatch(self, request: Request, call_next):
        """处理请求"""
        path = request.url.path
        method = request.method
        
        # 记录请求
        start_time = time.time()
        client_ip = self.get_client_ip(request)
        
        logger.debug(f"🌐 {method} {path} from {client_ip}")
        
        # 检查是否需要认证
        if not ENABLE_AUTH or self.is_public_path(path):
            # 不需要认证，直接处理
            response = await call_next(request)
            
            # 记录响应时间
            process_time = time.time() - start_time
            logger.debug(f"✅ {method} {path} -> {response.status_code} ({process_time:.3f}s)")
            
            return response
        
        # 需要认证
        if not self.auth_middleware:
            logger.error("认证中间件未初始化")
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"detail": "认证系统未初始化"}
            )
        
        try:
            # 执行认证
            auth_result = await self.auth_middleware.authenticate(request)
            
            if not auth_result.success:
                logger.warning(f"🚫 认证失败: {path} from {client_ip} - {auth_result.error}")
                
                return JSONResponse(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    content={
                        "detail": auth_result.error,
                        "type": "authentication_error"
                    },
                    headers={"WWW-Authenticate": "Bearer"}
                )
            
            # 认证成功，将认证信息添加到请求中
            request.state.auth_result = auth_result
            request.state.api_key = auth_result.api_key
            
            logger.debug(f"🔐 认证成功: {path} from {client_ip} - {auth_result.api_key.name}")
            
            # 处理请求
            response = await call_next(request)
            
            # 记录响应时间
            process_time = time.time() - start_time
            logger.debug(f"✅ {method} {path} -> {response.status_code} ({process_time:.3f}s)")
            
            # 添加速率限制头
            if auth_result.rate_limit_remaining is not None:
                response.headers["X-RateLimit-Remaining"] = str(auth_result.rate_limit_remaining)
            
            return response
            
        except Exception as e:
            logger.error(f"❌ 认证处理异常: {path} from {client_ip} - {e}")
            
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "detail": "认证处理失败",
                    "type": "authentication_error"
                }
            )
    
    def get_client_ip(self, request: Request) -> str:
        """获取客户端IP"""
        # 检查代理头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # 使用客户端IP
        if hasattr(request, "client") and request.client:
            return request.client.host
        
        return "unknown"


def create_global_auth_middleware(app):
    """创建并添加全局认证中间件"""
    if not ENABLE_AUTH:
        logger.info("认证功能已禁用，跳过全局认证中间件")
        return app

    try:
        # 获取认证管理器
        auth_manager = get_auth_manager()

        # 创建认证中间件
        auth_middleware = AuthMiddleware(auth_manager)

        # 添加全局认证中间件
        app.add_middleware(GlobalAuthMiddleware, auth_middleware=auth_middleware)

        logger.info("✅ 全局认证中间件已添加，所有API接口已受保护")

    except RuntimeError as e:
        logger.error(f"❌ 无法初始化全局认证中间件: {e}")
        logger.error("请确保在添加中间件前已初始化认证管理器")
        raise
    except Exception as e:
        logger.error(f"❌ 添加全局认证中间件失败: {e}")
        raise

    return app
