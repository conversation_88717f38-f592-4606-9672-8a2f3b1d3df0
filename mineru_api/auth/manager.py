# -*- encoding: utf-8 -*-
"""
认证管理器
"""

import asyncio
from typing import Optional, List
from loguru import logger

from .models import AuthConfig, APIKey, CreateAPIKeyRequest, APIKeyInfo
from .backends import get_auth_backend
from .backends.base import AuthBackend


class AuthManager:
    """认证管理器"""
    
    def __init__(self, config: AuthConfig):
        self.config = config
        self.backend: AuthBackend = get_auth_backend(config)
        self._cleanup_task: Optional[asyncio.Task] = None
        self._started = False
        logger.info(f"认证管理器已初始化，后端: {config.backend_type.value}")
    
    def _start_cleanup_task(self):
        """启动清理任务"""
        if self._started:
            return

        try:
            # 检查是否有运行的事件循环
            loop = asyncio.get_running_loop()
            if self._cleanup_task is None or self._cleanup_task.done():
                self._cleanup_task = loop.create_task(self._cleanup_loop())
                self._started = True
                logger.debug("清理任务已启动")
        except RuntimeError:
            # 没有运行的事件循环，稍后启动
            logger.debug("暂无事件循环，清理任务将稍后启动")
            pass
    
    async def _cleanup_loop(self):
        """清理循环"""
        logger.debug("认证清理任务已启动")
        while True:
            try:
                await asyncio.sleep(3600)  # 每小时执行一次
                cleaned_count = await self.cleanup_expired_keys()
                if cleaned_count > 0:
                    logger.info(f"清理了 {cleaned_count} 个过期的API key")
            except asyncio.CancelledError:
                logger.debug("认证清理任务已取消")
                break
            except Exception as e:
                logger.error(f"清理任务异常: {e}")
    
    async def verify_api_key(self, api_key: str, client_ip: Optional[str] = None) -> Optional[APIKey]:
        """验证API key"""
        # 确保清理任务已启动
        self._ensure_cleanup_task_started()

        try:
            return await self.backend.verify_api_key(api_key, client_ip)
        except Exception as e:
            logger.error(f"验证API key失败: {e}")
            return None
    
    async def create_api_key(self, request: CreateAPIKeyRequest) -> APIKey:
        """创建API key"""
        return await self.backend.create_api_key(request)
    
    async def revoke_api_key(self, api_key: str) -> bool:
        """撤销API key"""
        return await self.backend.revoke_api_key(api_key)
    
    async def list_api_keys(self, include_revoked: bool = False) -> List[APIKeyInfo]:
        """列出API key信息（不包含完整key）"""
        api_keys = await self.backend.list_api_keys()
        
        result = []
        for api_key in api_keys:
            # 过滤撤销的key
            if not include_revoked and api_key.status.value in ['revoked', 'expired']:
                continue
            
            # 创建不包含完整key的信息对象
            key_info = APIKeyInfo(
                key_prefix=f"{api_key.key[:8]}...",
                name=api_key.name,
                status=api_key.status,
                created_at=api_key.created_at,
                expires_at=api_key.expires_at,
                last_used_at=api_key.last_used_at,
                usage_count=api_key.usage_count,
                rate_limit=api_key.rate_limit,
                allowed_ips=api_key.allowed_ips,
                metadata=api_key.metadata
            )
            result.append(key_info)
        
        return result
    
    async def get_api_key_info(self, api_key: str) -> Optional[APIKey]:
        """获取API key详细信息"""
        return await self.backend.get_api_key_info(api_key)
    
    async def update_usage(self, api_key: str) -> None:
        """更新使用统计"""
        try:
            await self.backend.update_usage(api_key)
        except Exception as e:
            logger.error(f"更新使用统计失败: {e}")
    
    async def cleanup_expired_keys(self) -> int:
        """清理过期的API key"""
        try:
            return await self.backend.cleanup_expired_keys()
        except Exception as e:
            logger.error(f"清理过期API key失败: {e}")
            return 0
    
    async def get_statistics(self) -> dict:
        """获取统计信息"""
        try:
            api_keys = await self.backend.list_api_keys()
            
            stats = {
                "total": len(api_keys),
                "active": 0,
                "inactive": 0,
                "expired": 0,
                "revoked": 0,
                "total_usage": 0
            }
            
            for api_key in api_keys:
                stats[api_key.status.value] = stats.get(api_key.status.value, 0) + 1
                stats["total_usage"] += api_key.usage_count
            
            return stats
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}
    
    def _ensure_cleanup_task_started(self):
        """确保清理任务已启动"""
        if not self._started:
            self._start_cleanup_task()

    def stop(self):
        """停止管理器"""
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
        self._started = False


# 全局认证管理器实例（将在配置加载后初始化）
auth_manager: Optional[AuthManager] = None


def init_auth_manager(config: AuthConfig) -> AuthManager:
    """初始化认证管理器"""
    global auth_manager
    auth_manager = AuthManager(config)
    logger.info(f"认证管理器已初始化，使用后端: {config.backend_type}")
    return auth_manager


def get_auth_manager() -> AuthManager:
    """获取认证管理器实例"""
    if auth_manager is None:
        raise RuntimeError("认证管理器未初始化，请先调用 init_auth_manager")
    return auth_manager
