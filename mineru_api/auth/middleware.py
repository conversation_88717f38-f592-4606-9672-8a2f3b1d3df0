# -*- encoding: utf-8 -*-
"""
认证中间件
"""

import time
from typing import Optional, Dict, Any
from fastapi import Request, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from loguru import logger

from .manager import AuthManager
from .models import AuthResult


class AuthMiddleware:
    """认证中间件"""
    
    def __init__(self, auth_manager: AuthManager):
        self.auth_manager = auth_manager
        self.security = HTTPBearer(auto_error=False)
        self._rate_limit_cache: Dict[str, Dict[str, Any]] = {}
    
    async def authenticate(self, request: Request) -> AuthResult:
        """执行认证"""
        # 获取API key
        api_key = await self._extract_api_key(request)
        if not api_key:
            return AuthResult(
                success=False,
                error="缺少API key"
            )
        
        # 获取客户端IP
        client_ip = self._get_client_ip(request)
        
        # 验证API key
        api_key_obj = await self.auth_manager.verify_api_key(api_key, client_ip)
        if not api_key_obj:
            return AuthResult(
                success=False,
                error="无效的API key"
            )
        
        # 检查速率限制
        if self.auth_manager.config.enable_rate_limit:
            rate_limit_result = await self._check_rate_limit(api_key, api_key_obj.rate_limit)
            if not rate_limit_result["allowed"]:
                return AuthResult(
                    success=False,
                    error="超出速率限制",
                    rate_limit_remaining=rate_limit_result["remaining"]
                )
        
        # 更新使用统计
        await self.auth_manager.update_usage(api_key)
        
        return AuthResult(
            success=True,
            api_key=api_key_obj,
            rate_limit_remaining=rate_limit_result.get("remaining") if self.auth_manager.config.enable_rate_limit else None
        )
    
    async def _extract_api_key(self, request: Request) -> Optional[str]:
        """提取API key"""
        # 1. 从Authorization头提取
        authorization = request.headers.get("Authorization")
        if authorization:
            if authorization.startswith("Bearer "):
                return authorization[7:]
            elif authorization.startswith("ApiKey "):
                return authorization[7:]
        
        # 2. 从X-API-Key头提取
        api_key = request.headers.get("X-API-Key")
        if api_key:
            return api_key
        
        # 3. 从查询参数提取
        api_key = request.query_params.get("api_key")
        if api_key:
            return api_key
        
        return None
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP"""
        # 检查代理头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # 使用客户端IP
        if hasattr(request, "client") and request.client:
            return request.client.host
        
        return "unknown"
    
    async def _check_rate_limit(self, api_key: str, rate_limit: Optional[int]) -> Dict[str, Any]:
        """检查速率限制"""
        if not rate_limit:
            rate_limit = self.auth_manager.config.default_rate_limit
        
        current_time = time.time()
        window_start = int(current_time // 60) * 60  # 1分钟窗口
        
        # 获取或创建速率限制记录
        if api_key not in self._rate_limit_cache:
            self._rate_limit_cache[api_key] = {
                "window_start": window_start,
                "count": 0
            }
        
        cache_entry = self._rate_limit_cache[api_key]
        
        # 检查是否需要重置窗口
        if cache_entry["window_start"] < window_start:
            cache_entry["window_start"] = window_start
            cache_entry["count"] = 0
        
        # 检查是否超出限制
        if cache_entry["count"] >= rate_limit:
            return {
                "allowed": False,
                "remaining": 0
            }
        
        # 增加计数
        cache_entry["count"] += 1
        
        return {
            "allowed": True,
            "remaining": rate_limit - cache_entry["count"]
        }
    
    def cleanup_rate_limit_cache(self):
        """清理过期的速率限制缓存"""
        current_time = time.time()
        window_start = int(current_time // 60) * 60
        
        expired_keys = []
        for api_key, cache_entry in self._rate_limit_cache.items():
            if cache_entry["window_start"] < window_start - 60:  # 超过1分钟的缓存
                expired_keys.append(api_key)
        
        for key in expired_keys:
            del self._rate_limit_cache[key]


def create_auth_dependency(auth_manager: AuthManager):
    """创建认证依赖"""
    middleware = AuthMiddleware(auth_manager)
    
    async def auth_dependency(request: Request) -> AuthResult:
        """认证依赖函数"""
        result = await middleware.authenticate(request)
        
        if not result.success:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=result.error,
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        return result
    
    return auth_dependency
