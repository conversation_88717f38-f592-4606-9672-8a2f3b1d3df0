#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
API Key管理命令行工具
"""

import asyncio
import argparse
import sys
from pathlib import Path
from datetime import datetime
from typing import Optional

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from mineru_api.config import get_auth_config
from mineru_api.auth.manager import AuthManager
from mineru_api.auth.models import CreateAPIKeyRequest


class AuthCLI:
    """认证管理命令行工具"""
    
    def __init__(self):
        self.auth_manager = None
    
    async def init_manager(self):
        """初始化认证管理器"""
        if self.auth_manager is None:
            config = get_auth_config()
            self.auth_manager = AuthManager(config)
    
    async def create_key(self, name: str, expires_days: Optional[int] = None, 
                        rate_limit: Optional[int] = None, allowed_ips: Optional[str] = None):
        """创建API key"""
        await self.init_manager()
        
        # 解析IP列表
        ip_list = None
        if allowed_ips:
            ip_list = [ip.strip() for ip in allowed_ips.split(",")]
        
        request = CreateAPIKeyRequest(
            name=name,
            expires_days=expires_days,
            rate_limit=rate_limit,
            allowed_ips=ip_list
        )
        
        try:
            api_key = await self.auth_manager.create_api_key(request)
            
            print(f"✅ API Key创建成功!")
            print(f"名称: {api_key.name}")
            print(f"密钥: {api_key.key}")
            print(f"状态: {api_key.status.value}")
            print(f"创建时间: {api_key.created_at}")
            if api_key.expires_at:
                print(f"过期时间: {api_key.expires_at}")
            if api_key.rate_limit:
                print(f"速率限制: {api_key.rate_limit}/分钟")
            if api_key.allowed_ips:
                print(f"允许IP: {', '.join(api_key.allowed_ips)}")
            
            print(f"\n⚠️  请妥善保存API Key，它不会再次显示!")
            
        except Exception as e:
            print(f"❌ 创建API Key失败: {e}")
            return False
        
        return True
    
    async def list_keys(self, include_revoked: bool = False):
        """列出API keys"""
        await self.init_manager()
        
        try:
            api_keys = await self.auth_manager.list_api_keys(include_revoked=include_revoked)
            
            if not api_keys:
                print("📝 没有找到API Key")
                return
            
            print(f"📋 API Key列表 (共{len(api_keys)}个):")
            print("-" * 80)
            
            for key_info in api_keys:
                print(f"名称: {key_info.name}")
                print(f"密钥前缀: {key_info.key_prefix}")
                print(f"状态: {key_info.status.value}")
                print(f"创建时间: {key_info.created_at}")
                print(f"使用次数: {key_info.usage_count}")
                
                if key_info.expires_at:
                    print(f"过期时间: {key_info.expires_at}")
                if key_info.last_used_at:
                    print(f"最后使用: {key_info.last_used_at}")
                if key_info.rate_limit:
                    print(f"速率限制: {key_info.rate_limit}/分钟")
                if key_info.allowed_ips:
                    print(f"允许IP: {', '.join(key_info.allowed_ips)}")
                
                print("-" * 80)
                
        except Exception as e:
            print(f"❌ 列出API Key失败: {e}")
    
    async def revoke_key(self, api_key: str):
        """撤销API key"""
        await self.init_manager()
        
        try:
            success = await self.auth_manager.revoke_api_key(api_key)
            
            if success:
                print(f"✅ API Key已撤销: {api_key[:10]}...")
            else:
                print(f"❌ API Key不存在: {api_key[:10]}...")
                return False
                
        except Exception as e:
            print(f"❌ 撤销API Key失败: {e}")
            return False
        
        return True
    
    async def show_stats(self):
        """显示统计信息"""
        await self.init_manager()
        
        try:
            stats = await self.auth_manager.get_statistics()
            
            print("📊 认证统计信息:")
            print(f"总计: {stats.get('total', 0)}")
            print(f"活跃: {stats.get('active', 0)}")
            print(f"非活跃: {stats.get('inactive', 0)}")
            print(f"已过期: {stats.get('expired', 0)}")
            print(f"已撤销: {stats.get('revoked', 0)}")
            print(f"总使用次数: {stats.get('total_usage', 0)}")
            
        except Exception as e:
            print(f"❌ 获取统计信息失败: {e}")
    
    async def cleanup(self):
        """清理过期的API keys"""
        await self.init_manager()
        
        try:
            cleaned_count = await self.auth_manager.cleanup_expired_keys()
            print(f"🧹 已清理 {cleaned_count} 个过期的API Key")
            
        except Exception as e:
            print(f"❌ 清理失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="MineruAPI 认证管理工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 创建API key
    create_parser = subparsers.add_parser("create", help="创建API key")
    create_parser.add_argument("name", help="API key名称")
    create_parser.add_argument("--expires-days", type=int, help="过期天数")
    create_parser.add_argument("--rate-limit", type=int, help="速率限制(每分钟)")
    create_parser.add_argument("--allowed-ips", help="允许的IP地址(逗号分隔)")
    
    # 列出API keys
    list_parser = subparsers.add_parser("list", help="列出API keys")
    list_parser.add_argument("--include-revoked", action="store_true", help="包含已撤销的key")
    
    # 撤销API key
    revoke_parser = subparsers.add_parser("revoke", help="撤销API key")
    revoke_parser.add_argument("api_key", help="要撤销的API key")
    
    # 统计信息
    subparsers.add_parser("stats", help="显示统计信息")
    
    # 清理过期key
    subparsers.add_parser("cleanup", help="清理过期的API keys")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    cli = AuthCLI()
    
    try:
        if args.command == "create":
            asyncio.run(cli.create_key(
                args.name,
                args.expires_days,
                args.rate_limit,
                args.allowed_ips
            ))
        elif args.command == "list":
            asyncio.run(cli.list_keys(args.include_revoked))
        elif args.command == "revoke":
            asyncio.run(cli.revoke_key(args.api_key))
        elif args.command == "stats":
            asyncio.run(cli.show_stats())
        elif args.command == "cleanup":
            asyncio.run(cli.cleanup())
            
    except KeyboardInterrupt:
        print("\n操作已取消")
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
