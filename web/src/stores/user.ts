import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login, register, getUserInfo, logout, type UserInfo, type LoginParams, type RegisterParams } from '@/api/user'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

export const useUserStore = defineStore('user', () => {
  const router = useRouter()
  const token = ref(localStorage.getItem('token') || '')
  const userInfo = ref<UserInfo | null>(null)
  const isLoggedIn = ref(!!token.value)

  // 获取用户名
  const username = computed(() => userInfo.value?.username || '')

  // 设置token
  const setToken = (newToken: string) => {
    console.log('Setting token:', newToken)
    token.value = newToken
    localStorage.setItem('token', newToken)
    isLoggedIn.value = true
  }

  // 清除token
  const clearToken = () => {
    console.log('Clearing token')
    token.value = ''
    localStorage.removeItem('token')
    isLoggedIn.value = false
    userInfo.value = null
  }

  // 登录
  const handleLogin = async (params: LoginParams) => {
    try {
      const response = await login(params)
      console.log('Login response:', response)
      
      if (!response.access_token) {
        throw new Error('登录失败：无效的响应数据')
      }
      
      // 确保清除旧token
      clearToken()
      // 设置新token
      setToken(response.access_token)
      
      // 如果有用户信息则设置
      if (response.user) {
        userInfo.value = response.user
      }
      
      ElMessage.success('登录成功')
      // 不在这里进行路由跳转，由调用方决定跳转逻辑
    } catch (error: any) {
      console.error('Login error:', error)
      clearToken()
      ElMessage.error(error.message || '登录失败')
      throw error
    }
  }

  // 注册
  const handleRegister = async (params: RegisterParams) => {
    try {
      const response = await register(params)
      if (!response.access_token) {
        throw new Error('注册失败：无效的响应数据')
      }
      setToken(response.access_token)
      // 如果有用户信息则设置
      if (response.user) {
        userInfo.value = response.user
      }
      ElMessage.success('注册成功')
      router.push('/dashboard')
    } catch (error: any) {
      console.error('Register error:', error)
      ElMessage.error(error.message || '注册失败')
      throw error
    }
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    try {
      if (!token.value) return
      const user = await getUserInfo()
      userInfo.value = user
    } catch (error) {
      clearToken()
      router.push('/login')
    }
  }

  // 退出登录
  const handleLogout = async () => {
    try {
      await logout()
      clearToken()
      ElMessage.success('退出成功')
      router.push('/login')
    } catch (error: any) {
      ElMessage.error(error.message || '退出失败')
    }
  }

  return {
    token,
    userInfo,
    isLoggedIn,
    username,
    handleLogin,
    handleRegister,
    handleLogout,
    fetchUserInfo
  }
}) 