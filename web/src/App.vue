<template>
  <div class="app-container">
    <router-view></router-view>
  </div>
</template>

<script setup>
// 空的setup块，不需要导入任何组件
</script>

<style>
html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

#app {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.app-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.app-container > * {
  flex: 1;
  margin: 0;
  padding: 0;
}

:root {
  --el-menu-item-height: 50px;
}
</style>
