import request from '@/utils/request'

interface LoginData {
  username: string
  password: string
}

interface LoginResponse {
  access_token: string
  token_type: string
}

interface UserInfo {
  sub: string
  user_id: number
  is_admin: boolean
}

export const login = (data: LoginData) => {
  return request.post<LoginResponse>('/manage/login', data)
}

export const getUserInfo = () => {
  return request.get<UserInfo>('/manage/me')
} 