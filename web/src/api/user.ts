import request from '@/utils/request'

export interface UserInfo {
  id: string
  username: string
}

export interface LoginParams {
  username: string
  password: string
}

export interface RegisterParams extends LoginParams {
  confirm_password: string
}

export interface LoginResponse {
  access_token: string
  token_type: string
  user?: UserInfo
}

export interface RegisterResponse extends LoginResponse {}

// 登录
export const login = async (params: LoginParams): Promise<LoginResponse> => {
  const response = await request<LoginResponse>({
    url: '/manage/login',
    method: 'post',
    data: params
  })
  console.log('Login Response:', response)
  return response
}

// 注册
export const register = async (params: RegisterParams): Promise<RegisterResponse> => {
  const response = await request<RegisterResponse>({
    url: '/manage/register',
    method: 'post',
    data: params
  })
  console.log('Register Response:', response)
  return response
}

// 获取用户信息
export const getUserInfo = async (): Promise<UserInfo> => {
  return request({
    url: '/manage/me',
    method: 'get'
  })
}

// 退出登录
export const logout = async (): Promise<void> => {
  return request({
    url: '/manage/logout',
    method: 'post'
  })
}