import request from '@/utils/request'

// 任务趋势数据类型
export interface TaskTrendData {
  date: string
  successCount: number
  failedCount: number
}

// 系统状态数据类型
export interface SystemStatus {
  onlineNodes: number
  totalNodes: number
  documentNodes: number
  knowledgeNodes: number
  universalNodes: number
  queueTasks: number
  processingTasks: number
  successRate: number
  successTasks: number
  failedTasks: number
  avgLoad: number
}

// 今日统计数据类型
export interface TodayStats {
  submittedDocs: number
  docsTrend: number
  completedDocs: number
  completedTrend: number
  avgDuration: string
  durationTrend: number
}

// 错误记录类型
export interface RecentError {
  id: number
  taskId: string
  filename: string
  errorType: string
  message: string
  nodeName?: string
  timestamp: string
  stackTrace?: string
}

export interface NodeLoadData {
  times: string[]
  loadData: number[]
}

export interface ErrorDistribution {
  value: number
  name: string
}

export const dashboardApi = {
  // 获取系统状态
  getSystemStatus(): Promise<SystemStatus> {
    return request.get('/dashboard/system-status')
  },

  // 获取今日统计
  getTodayStats(): Promise<TodayStats> {
    return request.get('/dashboard/today-stats')
  },

  // 获取最近错误列表
  getRecentErrors(limit: number): Promise<RecentError[]> {
    return request.get('/dashboard/recent-errors', { params: { limit } })
  },

  // 重试任务
  retryTask(taskId: string): Promise<void> {
    return request.post(`/dashboard/tasks/${taskId}/retry`)
  },

  // 获取任务趋势数据
  getTaskTrend(days: number = 7): Promise<TaskTrendData[]> {
    return request.get('/dashboard/task-trend', { params: { days } })
  },

  // 获取节点负载数据
  getNodeLoad(hours: number = 24): Promise<NodeLoadData> {
    return request.get('/dashboard/node-load', { params: { hours } })
  },

  // 获取错误分布数据
  getErrorDistribution(): Promise<ErrorDistribution[]> {
    return request.get('/dashboard/error-distribution')
  }
}
