import request from '@/utils/request'
import type { 
  ParseTask, 
  ParseTaskStats,
  ParseMode
} from '@/types/mineruNode'

export interface GetTasksParams {
  skip?: number
  limit?: number
  status?: string
  user_id?: string
}

export interface TaskStatusResponse {
  task_id: string
  status: string
  progress?: number
  result?: Record<string, any>
  error?: string
}

export interface TaskResultResponse {
  task_id: string
  status: string
  result: Record<string, any>
  output_path?: string
  completed_at?: string
}

export const parseTaskApi = {
  // 通过文件上传创建解析任务（新版本，使用almond_parser统一接口）
  uploadFile(
    file: File,
    parse_mode: ParseMode = 'auto',
    service_type: string = 'auto',
    priority: number = 1,
    max_retries: number = 2,
    document_id?: string
  ): Promise<any> {
    const formData = new FormData()
    formData.append('files', file)  // 注意：后端期望的是files字段
    formData.append('parse_mode', parse_mode)
    formData.append('service_type', service_type)
    formData.append('priority', priority.toString())
    formData.append('max_retries', max_retries.toString())
    if (document_id) {
      formData.append('document_id', document_id)
    }

    return request.post('/manage/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 兼容旧版本的上传接口（保留以防需要）
  uploadFileLegacy(
    file: File,
    parse_mode: ParseMode = 'auto',
    document_id?: string,
    max_retries: number = 2
  ): Promise<ParseTask> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('parse_mode', parse_mode)
    if (document_id) {
      formData.append('document_id', document_id)
    }
    formData.append('max_retries', max_retries.toString())

    return request.post('/mineru-parse/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 通过 base64 内容创建解析任务
  createTask(data: {
    file_content: string
    filename: string
    parse_config?: Record<string, any>
    parse_mode?: ParseMode
    document_id?: string
    max_retries?: number
  }): Promise<ParseTask> {
    return request.post('/mineru-parse/', data)
  },

  // 获取解析任务列表
  getTasks(params?: GetTasksParams): Promise<ParseTask[]> {
    return request.get('/mineru-parse/', { params })
  },

  // 获取解析任务统计
  getStats(): Promise<ParseTaskStats> {
    return request.get('/mineru-parse/stats')
  },

  // 获取单个解析任务
  getTask(taskId: string): Promise<ParseTask> {
    return request.get(`/mineru-parse/${taskId}`)
  },

  // 获取解析任务状态（简化版本）
  getTaskStatus(taskId: string): Promise<TaskStatusResponse> {
    return request.get(`/mineru-parse/${taskId}/status`)
  },

  // 获取解析任务结果
  getTaskResult(taskId: string): Promise<TaskResultResponse> {
    return request.get(`/mineru-parse/${taskId}/result`)
  },

  // 删除解析任务
  deleteTask(taskId: string): Promise<{ message: string }> {
    return request.delete(`/mineru-parse/${taskId}`)
  }
}
