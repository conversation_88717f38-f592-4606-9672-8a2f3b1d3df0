<template>
  <div class="login-bg">
    <div class="login-panel">
      <div class="login-logo-area">
        <img src="../assets/logo.svg" alt="Logo" class="login-logo" />
        <div class="login-title">杏仁解析</div>
      </div>
      <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" class="login-form"
        @submit.prevent="handleLogin">
        <el-form-item prop="username">
          <el-input v-model="loginForm.username" size="large" :prefix-icon="User" placeholder="用户名" autofocus
            clearable />
        </el-form-item>
        <el-form-item prop="password">
          <el-input v-model="loginForm.password" size="large" :prefix-icon="Lock" type="password" placeholder="密码"
            show-password clearable />
        </el-form-item>
        <div class="login-options">
          <el-checkbox v-model="rememberMe" size="large">记住我</el-checkbox>
          <el-link type="primary" :underline="false" class="forget-link">忘记密码？</el-link>
        </div>
        <el-button type="primary" size="large" class="login-btn" :loading="loading" native-type="submit" round>
          {{ loading ? '登录中...' : '登 录' }}
        </el-button>
        <div class="register-tip">
          <span class="tip-text">还没有账号？</span>
          <el-link type="primary" :underline="false" @click="showRegister">立即注册</el-link>
        </div>
      </el-form>
    </div>
    <div class="login-footer">
      <span>© {{ year }} 杏仁解析</span>
    </div>
  </div>

  <RegisterDialog v-model:visible="registerVisible" />
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Lock } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { useUserStore } from '@/stores/user'
import RegisterDialog from '@/views/RegisterDialog.vue'

const router = useRouter()
const userStore = useUserStore()
const loginFormRef = ref<FormInstance>()
const loading = ref(false)
const rememberMe = ref(false)
const year = new Date().getFullYear()
const registerVisible = ref(false)

const loginForm = reactive({
  username: '',
  password: ''
})

const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, message: '用户名不能少于3个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码不能少于6个字符', trigger: 'blur' }
  ]
}

onMounted(() => {
  // 如果之前记住了用户名，自动填充
  const rememberedUsername = localStorage.getItem('remember_username')
  if (rememberedUsername) {
    loginForm.username = rememberedUsername
    rememberMe.value = true
  }
})

const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    await loginFormRef.value.validate()
    loading.value = true

    if (rememberMe.value) {
      localStorage.setItem('remember_username', loginForm.username)
    } else {
      localStorage.removeItem('remember_username')
    }

    await userStore.handleLogin(loginForm)

    // 获取重定向地址，默认跳转到 dashboard
    const redirect = router.currentRoute.value.query.redirect as string
    router.push(redirect || '/dashboard')
  } catch (error: any) {
    console.error('登录失败:', error)
    if (!error.message?.includes('请输入')) {
      ElMessage.error(error.message || '登录失败，请检查用户名和密码')
    }
  } finally {
    loading.value = false
  }
}

const showRegister = () => {
  registerVisible.value = true
}
</script>

<style scoped>
.login-bg {
  min-height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #f8fafc 0%, #e8f0fe 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.login-panel {
  background: #fff;
  box-shadow: 0 6px 32px 0 rgba(0, 38, 107, 0.12);
  border-radius: 18px;
  padding: 42px 36px 32px 36px;
  min-width: 360px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 3vh;
}

.login-logo-area {
  text-align: center;
  margin-bottom: 32px;
}

.login-logo {
  width: 64px;
  height: 64px;
  margin-bottom: 12px;
}

.login-title {
  font-size: 28px;
  color: #1559ed;
  font-weight: 700;
  letter-spacing: 1px;
  margin-bottom: 4px;
}

.login-desc {
  font-size: 15px;
  color: #98a6ad;
  margin-bottom: 0;
}

.login-form {
  width: 100%;
  margin-top: 8px;
}

:deep(.el-input__wrapper) {
  border-radius: 10px;
  background: #f6f8fc;
  border: 1px solid #e7eaf3;
  box-shadow: none;
  transition: border-color 0.2s;
}

:deep(.el-input__wrapper:hover),
:deep(.el-input__wrapper.is-focus) {
  border-color: #1559ed;
  background: #f0f5ff;
}

:deep(.el-input__inner) {
  font-size: 16px;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 0 18px 0;
  font-size: 14px;
}

.forget-link {
  font-size: 13px;
}

.login-btn {
  width: 100%;
  height: 42px;
  font-size: 17px;
  letter-spacing: 2px;
  border-radius: 24px;
  margin-top: 8px;
}

.login-footer {
  margin-top: 30px;
  color: #b0b8c1;
  font-size: 14px;
  text-align: center;
  width: 100%;
}

.register-tip {
  text-align: center;
  margin-top: 16px;
  font-size: 14px;
  color: #606266;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.register-tip .tip-text {
  color: #909399;
}

.register-tip :deep(.el-link) {
  font-size: 14px;
  font-weight: 500;
}

.register-tip :deep(.el-link:hover) {
  text-decoration: underline;
}

@media (max-width: 600px) {
  .login-panel {
    min-width: unset;
    width: 95vw;
    padding: 32px 8vw 26px 8vw;
  }
}

@media (prefers-color-scheme: dark) {
  .login-bg {
    background: linear-gradient(135deg, #171923 0%, #222235 100%);
  }

  .login-panel {
    background: #222235;
    box-shadow: 0 4px 28px 0 rgba(21, 89, 237, 0.12);
  }

  .login-title {
    color: #4f8cff;
  }

  .login-desc,
  .login-footer {
    color: #888db6;
  }

  :deep(.el-input__wrapper) {
    background: #181a26;
    border: 1px solid #3c3f4d;
    color: #eee;
  }

  :deep(.el-input__wrapper.is-focus) {
    border-color: #4f8cff;
    background: #262d44;
  }

  .register-tip .tip-text {
    color: #888db6;
  }
}
</style>