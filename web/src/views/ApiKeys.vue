<template>
  <div class="api-keys-container">
    <div class="page-header">
      <h2>API 密钥管理</h2>
      <el-button type="primary" @click="handleCreateKey">
        <el-icon><Plus /></el-icon>
        创建密钥
      </el-button>
    </div>

    <el-table
      v-loading="loading"
      :data="apiKeys"
      border
      style="width: 100%"
    >
      <el-table-column prop="name" label="密钥名称" min-width="150" />
      <el-table-column prop="key" label="API 密钥" min-width="300" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="key-wrapper">
            <span>{{ maskApiKey(row.key) }}</span>
            <el-button
              type="primary"
              link
              size="small"
              @click="copyApiKey(row.key)"
            >
              <el-icon><DocumentCopy /></el-icon>
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间" width="180">
        <template #default="{ row }">
          {{ formatDateTime(row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column prop="last_used_at" label="最后使用" width="180">
        <template #default="{ row }">
          {{ row.last_used_at ? formatDateTime(row.last_used_at) : '从未使用' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" fixed="right">
        <template #default="{ row }">
          <el-button
            type="danger"
            link
            size="small"
            @click="handleDeleteKey(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 创建密钥对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="创建 API 密钥"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="密钥名称" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入密钥名称"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, DocumentCopy } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/format'
import { getApiKeys, createApiKey, deleteApiKey } from '@/api/apiKey'
import type { ApiKey } from '@/api/apiKey'

const loading = ref(false)
const dialogVisible = ref(false)
const submitting = ref(false)
const apiKeys = ref<ApiKey[]>([])

const formData = reactive({
  name: ''
})

const rules = {
  name: [
    { required: true, message: '请输入密钥名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ]
}

// 加载API密钥列表
const loadApiKeys = async () => {
  loading.value = true
  try {
    const response = await getApiKeys()
    console.log('API Keys response:', response)
    apiKeys.value = Array.isArray(response) ? response : []
  } catch (error) {
    console.error('获取API密钥列表失败:', error)
    ElMessage.error('获取API密钥列表失败')
  } finally {
    loading.value = false
  }
}

// 创建新密钥
const handleCreateKey = () => {
  formData.name = ''
  dialogVisible.value = true
}

// 提交创建
const handleSubmit = async () => {
  submitting.value = true
  try {
    const response = await createApiKey(formData)
    console.log('Create API Key response:', response)
    const newApiKey = Array.isArray(response) ? response[0] : response
    apiKeys.value.unshift(newApiKey)
    ElMessage.success('创建成功')
    dialogVisible.value = false
  } catch (error: any) {
    console.error('创建失败:', error)
    // 显示具体的错误信息
    ElMessage.error(error.message || '创建失败')
  } finally {
    submitting.value = false
  }
}

// 删除密钥
const handleDeleteKey = async (row: ApiKey) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除该API密钥吗？删除后无法恢复，且使用该密钥的应用将无法继续访问。',
      '警告',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await deleteApiKey(row.id)
    const index = apiKeys.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      apiKeys.value.splice(index, 1)
    }
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 复制API密钥
const copyApiKey = async (key: string) => {
  try {
    const textArea = document.createElement('textarea')
    textArea.value = key
    textArea.style.position = 'fixed'
    textArea.style.left = '-9999px'
    textArea.style.top = '0'
    document.body.appendChild(textArea)
    textArea.select()
    textArea.setSelectionRange(0, textArea.value.length)
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('复制成功')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

// 掩码显示API密钥
const maskApiKey = (key: string) => {
  if (!key) return ''
  return `${key.slice(0, 8)}...${key.slice(-8)}`
}

// 初始加载
loadApiKeys()
</script>

<style scoped>
.api-keys-container {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
}

.key-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 