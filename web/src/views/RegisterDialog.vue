<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建账号"
    width="400px"
    :close-on-click-modal="false"
    @closed="handleClosed"
    class="register-dialog"
    :show-close="true"
  >
    <div class="dialog-header">
      <h2 class="dialog-title">欢迎加入</h2>
      <p class="dialog-subtitle">创建一个账号开始使用全部功能</p>
    </div>
    
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      class="register-form"
      size="large"
    >
      <el-form-item prop="username">
        <el-input
          v-model="formData.username"
          placeholder="请输入用户名"
          :prefix-icon="User"
          clearable
        />
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="formData.password"
          type="password"
          placeholder="请输入密码"
          :prefix-icon="Lock"
          show-password
          clearable
        />
      </el-form-item>
      <el-form-item prop="confirm_password">
        <el-input
          v-model="formData.confirm_password"
          type="password"
          placeholder="请再次输入密码"
          :prefix-icon="Lock"
          show-password
          clearable
        />
      </el-form-item>

      <div class="form-actions">
        <el-button
          type="primary"
          class="submit-btn"
          @click="handleSubmit"
          :loading="loading"
          round
        >
          {{ loading ? '注册中...' : '立即注册' }}
        </el-button>
      </div>
    </el-form>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { User, Lock } from '@element-plus/icons-vue'

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
}>()

const userStore = useUserStore()
const formRef = ref<FormInstance>()
const loading = ref(false)

const formData = reactive({
  username: '',
  password: '',
  confirm_password: ''
})

const validatePass2 = (rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== formData.password) {
    callback(new Error('两次输入密码不一致!'))
  } else {
    callback()
  }
}

const rules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirm_password: [
    { required: true, message: '请再次输入密码', trigger: 'blur' },
    { validator: validatePass2, trigger: 'blur' }
  ]
}

const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
})

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    await userStore.handleRegister(formData)
    dialogVisible.value = false
  } catch (error) {
    // 验证失败或注册失败的错误已在store中处理
  } finally {
    loading.value = false
  }
}

const handleClosed = () => {
  if (!formRef.value) return
  formRef.value.resetFields()
}
</script>

<style scoped>
.register-dialog :deep(.el-dialog) {
  border-radius: 16px;
  box-shadow: 0 12px 32px 4px rgba(0, 0, 0, .04), 0 8px 20px rgba(0, 0, 0, .08);
}

.register-dialog :deep(.el-dialog__header) {
  margin: 0;
  padding: 0;
  border-bottom: none;
}

.register-dialog :deep(.el-dialog__headerbtn) {
  top: 16px;
  right: 16px;
}

.register-dialog :deep(.el-dialog__body) {
  padding: 0 32px 32px;
}

.dialog-header {
  text-align: center;
  padding: 32px 0;
}

.dialog-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px;
}

.dialog-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.register-form {
  margin-top: 8px;
}

.register-form :deep(.el-input__wrapper) {
  border-radius: 12px;
  background: #f6f8fc;
  border: 1px solid #e7eaf3;
  box-shadow: none;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.register-form :deep(.el-input__wrapper:hover),
.register-form :deep(.el-input__wrapper.is-focus) {
  border-color: #1559ed;
  background: #f0f5ff;
}

.register-form :deep(.el-input__inner) {
  font-size: 15px;
  height: 24px;
  line-height: 24px;
}

.register-form :deep(.el-form-item__error) {
  padding-left: 12px;
}

.form-actions {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

.submit-btn {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 1px;
}

@media (prefers-color-scheme: dark) {
  .dialog-title {
    color: #e5eaf3;
  }

  .dialog-subtitle {
    color: #888db6;
  }

  .register-dialog :deep(.el-dialog) {
    background: #222235;
    border: 1px solid #3c3f4d;
  }

  .register-form :deep(.el-input__wrapper) {
    background: #181a26;
    border: 1px solid #3c3f4d;
    color: #e5eaf3;
  }

  .register-form :deep(.el-input__wrapper:hover),
  .register-form :deep(.el-input__wrapper.is-focus) {
    border-color: #4f8cff;
    background: #262d44;
  }
}
</style> 