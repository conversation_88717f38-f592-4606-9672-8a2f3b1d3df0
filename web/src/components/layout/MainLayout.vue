<template>
  <el-container class="app-wrapper">
    <el-aside :class="['sidebar-container', { 'is-collapsed': isCollapsed }]">
      <Sidebar :is-collapsed="isCollapsed" @update:is-collapsed="isCollapsed = $event" />
    </el-aside>
    <el-main class="app-main">
      <router-view v-slot="{ Component }">
        <transition name="fade-transform" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </el-main>
  </el-container>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Sidebar from './Sidebar.vue'

const isCollapsed = ref(false)
</script>

<style scoped>
.app-wrapper {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.sidebar-container {
  background-color: #263445;
  transition: width 0.15s ease-out;
  overflow: hidden;
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.12);
  width: 200px;
  contain: layout style;
}

.sidebar-container.is-collapsed {
  width: 64px;
}

.app-main {
  padding: 20px;
  overflow-x: hidden;
  background: #f0f2f5;
}

.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style> 