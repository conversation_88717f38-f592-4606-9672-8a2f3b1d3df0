<template>
  <div class="filter-container">
    <el-form :inline="true" :model="queryParams" class="filter-form">
      <div class="form-group">
        <el-form-item label="批次ID">
          <el-input
            v-model="queryParams.batch_id"
            placeholder="请输入批次ID"
            clearable
            :prefix-icon="Search"
          />
        </el-form-item>
        <el-form-item label="文档ID">
          <el-input
            v-model="queryParams.document_id"
            placeholder="请输入文档ID"
            clearable
            :prefix-icon="Document"
          />
        </el-form-item>
        <el-form-item label="文件名">
          <el-input
            v-model="queryParams.file_name"
            placeholder="请输入文件名"
            clearable
            :prefix-icon="Files"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            class="status-select"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
              <div class="status-option">
                <el-tag :type="item.type" size="small" class="status-tag">
                  {{ item.label }}
                </el-tag>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
      </div>

      <div class="form-actions">
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleSearch">
            查询
          </el-button>
          <el-button :icon="Refresh" @click="handleReset">
            重置
          </el-button>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { Search, Document, Files, Refresh } from '@element-plus/icons-vue'
import { QueryParams, StatusOption } from '@/types/document'

const props = defineProps<{
  queryParams: QueryParams
}>()

const emit = defineEmits<{
  (e: 'search'): void
  (e: 'reset'): void
}>()

// 状态选项
const statusOptions: StatusOption[] = [
  { value: 'UPLOADING', label: '上传中', type: 'info' },
  { value: 'UPLOADED', label: '已上传', type: 'warning' },
  { value: 'PARSING', label: '解析中', type: 'warning' },
  { value: 'COMPLETED', label: '已完成', type: 'success' },
  { value: 'FAILED', label: '失败', type: 'danger' },
  { value: 'RETRY_PENDING', label: '等待重试', type: 'warning' },
  { value: 'FALLBACK_RETRY', label: '降级重试', type: 'warning' }
]

const handleSearch = () => {
  emit('search')
}

const handleReset = () => {
  emit('reset')
}
</script>

<style scoped>
.filter-container {
  padding: 16px 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin: 0;
  align-items: flex-end;
}

.form-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.form-actions {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  margin-left: 16px;
}

:deep(.el-form-item) {
  margin-bottom: 0;
  margin-right: 0;
}

:deep(.el-form-item__label) {
  color: #374151;
  font-weight: 500;
  font-size: 14px;
}

:deep(.el-input),
:deep(.el-select) {
  width: 180px;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

:deep(.el-input__wrapper:hover) {
  border-color: #3b82f6;
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.15);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
  padding: 10px 20px;
  transition: all 0.2s ease;
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: none;
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.3);
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

:deep(.el-button--default) {
  background: #fff;
  border: 1px solid #e5e7eb;
  color: #374151;
}

:deep(.el-button--default:hover) {
  border-color: #3b82f6;
  color: #3b82f6;
  background: #f8fafc;
}

.status-select {
  width: 180px;
}

.status-option {
  display: flex;
  align-items: center;
  padding: 4px 0;
}

.status-tag {
  width: 100%;
  text-align: center;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .filter-container {
    padding: 8px;
  }

  .filter-form {
    gap: 8px;
  }

  .form-group {
    flex-direction: column;
    width: 100%;
  }

  .form-actions {
    width: 100%;
    justify-content: flex-end;
  }

  :deep(.el-input),
  :deep(.el-select),
  .status-select {
    width: 100%;
  }
}
</style> 