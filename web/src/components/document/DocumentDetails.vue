<template>
  <el-dialog
    v-model="dialogVisible"
    title="文档详情"
    width="90%"
    :close-on-click-modal="false"
    class="document-details-dialog"
    :fullscreen="true"
    destroy-on-close
  >
    <div class="dialog-content">
      <el-tabs type="border-card" class="details-tabs">
        <el-tab-pane label="基本信息">
          <div class="basic-info-content">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="文档ID" :span="2">{{ document?.document_id }}</el-descriptions-item>
              <el-descriptions-item label="批次ID" :span="2">{{ document?.batch_id }}</el-descriptions-item>
              <el-descriptions-item label="文件名">{{ document?.file_name }}</el-descriptions-item>
              <el-descriptions-item label="文件大小">{{ formatFileSize(document?.file_size) }}</el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="getStatusType(document?.status)">
                  {{ getStatusText(document?.status) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="处理阶段">
                <el-tag :type="getProcessingStageType(document?.processing_stage)">
                  {{ getProcessingStageText(document?.processing_stage) }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>

        <el-tab-pane label="解析结果" v-if="document?.status === 'COMPLETED'">
          <div v-loading="loadingResult" class="parse-result-content">
            <div v-if="parseResult" class="markdown-container">
              <div class="markdown-toolbar">
                <el-button size="small" @click="copyMarkdown">
                  <el-icon><DocumentCopy /></el-icon>
                  复制内容
                </el-button>
              </div>
              <div class="markdown-content" v-html="renderedMarkdown"></div>
            </div>
            <el-empty v-else description="暂无解析结果" />
          </div>
        </el-tab-pane>

        <el-tab-pane label="处理结果" v-if="document?.result">
          <div class="json-content">
            <div class="json-container">
              <div class="json-toolbar">
                <el-button size="small" @click="copyJson(document.result)">
                  <el-icon><DocumentCopy /></el-icon>
                  复制JSON
                </el-button>
              </div>
              <div class="json-viewer">
                <pre>{{ JSON.stringify(document.result, null, 2) }}</pre>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="MaxKB结果" v-if="document?.maxkb_result">
          <div class="json-content">
            <div class="json-container">
              <div class="json-toolbar">
                <el-button size="small" @click="copyJson(document.maxkb_result)">
                  <el-icon><DocumentCopy /></el-icon>
                  复制JSON
                </el-button>
              </div>
              <div class="json-viewer">
                <pre>{{ JSON.stringify(document.maxkb_result, null, 2) }}</pre>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="错误信息" v-if="document?.error_message">
          <div class="error-content">
            <el-alert
              :title="document.error_message"
              type="error"
              :closable="false"
              show-icon
            />
          </div>
        </el-tab-pane>

        <el-tab-pane label="进度信息" v-if="document?.ragflow_progress_msg">
          <div class="progress-content">
            <el-alert
              :title="document.ragflow_progress_msg"
              type="info"
              :closable="false"
              show-icon
            />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { DocumentCopy } from '@element-plus/icons-vue'
import { Document, DocumentParseResult } from '@/types/document'
import { formatFileSize } from '@/utils/format'
import { getStatusType, getStatusText, getProcessingStageType, getProcessingStageText } from '@/utils/status'
import { getDocumentResult } from '@/api/document'
import MarkdownIt from 'markdown-it'

const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true
})

const props = defineProps<{
  visible: boolean
  document: Document | null
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
}>()

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const loadingResult = ref(false)
const parseResult = ref<DocumentParseResult | null>(null)

const renderedMarkdown = computed(() => {
  if (!parseResult.value?.result.markdown_text) return ''
  return md.render(parseResult.value.result.markdown_text)
})

// 复制 Markdown 内容
const copyMarkdown = () => {
  if (!parseResult.value?.result.markdown_text) return

  try {
    navigator.clipboard.writeText(parseResult.value.result.markdown_text)
    ElMessage.success('Markdown 内容已复制到剪贴板')
  } catch (err) {
    console.error('复制失败:', err)
    ElMessage.error('复制失败')
  }
}

// 复制 JSON 内容
const copyJson = (data: any) => {
  try {
    navigator.clipboard.writeText(JSON.stringify(data, null, 2))
    ElMessage.success('JSON 内容已复制到剪贴板')
  } catch (err) {
    console.error('复制失败:', err)
    ElMessage.error('复制失败')
  }
}

// 监听文档变化，获取解析结果
watch(() => props.document, async (newDoc) => {
  if (newDoc?.status === 'COMPLETED') {
    loadingResult.value = true
    try {
      console.log('获取解析结果:', newDoc.document_id)
      const data = await getDocumentResult(newDoc.document_id)
      console.log('解析结果:', data)
      parseResult.value = data
    } catch (err: any) {
      console.error('获取解析结果失败:', err)
      parseResult.value = null
    } finally {
      loadingResult.value = false
    }
  } else {
    parseResult.value = null
  }
}, { immediate: true })
</script>

<style scoped>
.document-details-dialog :deep(.el-dialog) {
  margin: 5vh auto 50px !important;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.document-details-dialog :deep(.el-dialog__header) {
  margin: 0;
  padding: 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.document-details-dialog :deep(.el-dialog__body) {
  flex: 1;
  padding: 0;
  height: calc(90vh - 130px);
  position: relative;
  overflow: auto;
}

.dialog-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.details-tabs {
  height: 100%;
  display: flex;
  /* flex-direction: column; */
  border: none;
}

.details-tabs :deep(.el-tabs__header) {
  margin: 0 !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 1000 !important;
  background: #fff !important;
  border-bottom: 1px solid var(--el-border-color-lighter) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  flex-shrink: 0 !important;
}

/* 额外的选择器确保覆盖Element Plus默认样式 */
.document-details-dialog .el-tabs--border-card > .el-tabs__header {
  margin: 0 !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 1000 !important;
  background: #fff !important;
  border-bottom: 1px solid var(--el-border-color-lighter) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

/* 最终解决方案：使用transform和固定定位 */
.document-details-dialog {
  position: relative;
}

.document-details-dialog .el-tabs__header {
  position: fixed !important;
  top: 60px !important; /* dialog header 高度 */
  left: 5% !important; /* 对应dialog的width="90%" */
  right: 5% !important;
  z-index: 2000 !important;
  background: #fff !important;
  border-bottom: 1px solid var(--el-border-color-lighter) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  margin: 0 !important;
}

/* 为内容区域添加顶部间距，避免被固定header遮挡 */
.document-details-dialog .el-tabs__content {
  padding-top: 60px !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .document-details-dialog .el-tabs__header {
    left: 0 !important;
    right: 0 !important;
    top: 50px !important;
  }
}

.details-tabs :deep(.el-tabs__content) {
  flex: 1;
  overflow: auto;
  padding: 0;
}

.details-tabs :deep(.el-tab-pane) {
  padding: 20px;
}

.basic-info-content,
.parse-result-content,
.json-content,
.error-content,
.progress-content {
  /* 移除固定高度，让内容自然流动 */
}

.markdown-container,
.json-container {
  display: flex;
  flex-direction: column;
}

.markdown-toolbar,
.json-toolbar {
  padding: 12px 20px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  background: var(--el-bg-color-page);
  position: sticky;
  top: 0;
  z-index: 50;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.markdown-content {
  padding: 20px;
  background: var(--el-bg-color);
}

.json-viewer {
  padding: 20px;
  background: var(--el-bg-color-page);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

.json-viewer pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-size: 13px;
  line-height: 1.5;
}

.error-content,
.progress-content {
  padding: 20px;
  overflow: auto;
}

/* Markdown 样式优化 */
.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3),
.markdown-content :deep(h4),
.markdown-content :deep(h5),
.markdown-content :deep(h6) {
  margin: 24px 0 16px 0;
  font-weight: 600;
  line-height: 1.25;
  color: var(--el-text-color-primary);
}

.markdown-content :deep(h1) {
  font-size: 28px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  padding-bottom: 8px;
}

.markdown-content :deep(h2) {
  font-size: 24px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  padding-bottom: 6px;
}

.markdown-content :deep(h3) {
  font-size: 20px;
}

.markdown-content :deep(h4) {
  font-size: 18px;
}

.markdown-content :deep(h5) {
  font-size: 16px;
}

.markdown-content :deep(h6) {
  font-size: 14px;
}

.markdown-content :deep(p) {
  margin: 16px 0;
  line-height: 1.6;
}

.markdown-content :deep(img) {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 16px 0;
}

.markdown-content :deep(pre) {
  background: #f6f8fa;
  padding: 16px;
  border-radius: 6px;
  overflow-x: auto;
  margin: 16px 0;
  border: 1px solid var(--el-border-color-lighter);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 13px;
  line-height: 1.45;
}

.markdown-content :deep(code) {
  background: #f6f8fa;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 13px;
  color: var(--el-color-danger);
}

.markdown-content :deep(pre code) {
  background: none;
  padding: 0;
  color: inherit;
}

.markdown-content :deep(blockquote) {
  margin: 16px 0;
  padding: 0 16px;
  border-left: 4px solid var(--el-color-primary);
  background: var(--el-color-primary-light-9);
  color: var(--el-text-color-secondary);
}

.markdown-content :deep(ul),
.markdown-content :deep(ol) {
  margin: 16px 0;
  padding-left: 24px;
}

.markdown-content :deep(li) {
  margin: 8px 0;
}

.markdown-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
  border: 1px solid var(--el-border-color);
}

.markdown-content :deep(th),
.markdown-content :deep(td) {
  padding: 12px;
  text-align: left;
  border: 1px solid var(--el-border-color);
}

.markdown-content :deep(th) {
  background: var(--el-bg-color-page);
  font-weight: 600;
}

.markdown-content :deep(a) {
  color: var(--el-color-primary);
  text-decoration: none;
}

.markdown-content :deep(a:hover) {
  text-decoration: underline;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .document-details-dialog :deep(.el-dialog) {
    margin: 0 !important;
    max-height: 100vh;
  }

  .document-details-dialog :deep(.el-dialog__body) {
    height: calc(100vh - 150px);
  }



  .markdown-toolbar,
  .json-toolbar {
    padding: 12px 16px;
  }

  .markdown-content,
  .json-viewer,
  .basic-info-content,
  .error-content,
  .progress-content {
    padding: 16px;
  }
}
</style>