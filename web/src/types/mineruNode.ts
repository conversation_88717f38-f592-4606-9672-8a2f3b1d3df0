// MinerU 节点相关类型定义

export type NodeStatus = 'online' | 'offline' | 'busy' | 'error'
export type ParseMode = 'pipeline' | 'sglang' | 'auto'
export type ServiceType = 'document' | 'knowledge_base' | 'universal'
export type TaskStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'retry'

export interface MinerUNode {
  id: number
  name: string
  host: string
  port: number
  base_url: string
  parse_mode: ParseMode
  service_type: ServiceType
  max_concurrent_tasks: number
  priority: number
  status: NodeStatus
  current_tasks: number
  total_tasks: number
  success_tasks: number
  failed_tasks: number
  last_health_check: string | null
  health_check_interval: number
  consecutive_failures: number
  auth_token: string | null
  version: string | null
  description: string | null
  tags: Record<string, any> | null
  is_enabled: boolean
  created_at: string
  updated_at: string
  // 前端临时状态
  checking?: boolean
  detecting?: boolean
}

export interface MinerUNodeCreate {
  name: string
  host: string
  port: number
  parse_mode: ParseMode
  service_type?: ServiceType
  max_concurrent_tasks?: number
  priority?: number
  health_check_interval?: number
  auth_token?: string
  description?: string
  tags?: Record<string, any>
}

export interface MinerUNodeUpdate {
  name?: string
  service_type?: ServiceType
  max_concurrent_tasks?: number
  priority?: number
  health_check_interval?: number
  auth_token?: string
  description?: string
  tags?: Record<string, any>
  is_enabled?: boolean
}

export interface MinerUNodeStats {
  total_nodes: number
  online_nodes: number
  offline_nodes: number
  busy_nodes: number
  error_nodes: number
  total_tasks: number
  success_tasks: number
  failed_tasks: number
  running_tasks: number
  success_rate: number
}

export interface ParseTask {
  id: number
  task_id: string
  node_id: number | null
  user_id: string
  document_id: string | null
  filename: string
  file_size: number
  file_type: string
  parse_mode: ParseMode
  parse_config: Record<string, any> | null
  status: TaskStatus
  progress: number | null
  error_message: string | null
  retry_count: number
  max_retries: number
  retry_nodes: number[] | null
  result_data: Record<string, any> | null
  output_path: string | null
  started_at: string | null
  completed_at: string | null
  created_at: string
  updated_at: string
}

export interface ParseTaskCreate {
  user_id: string
  document_id?: string
  filename: string
  file_size: number
  file_type: string
  parse_mode?: ParseMode
  parse_config?: Record<string, any>
  max_retries?: number
}

export interface ParseTaskStats {
  total_tasks: number
  pending_tasks: number
  processing_tasks: number
  completed_tasks: number
  failed_tasks: number
  success_rate: number
}

export interface NodeHealthStatus {
  node_id: number
  node_name: string
  is_healthy: boolean
  last_check: string | null
  consecutive_failures: number
  response_time: number | null
  error_message: string | null
}

export interface HealthCheckRecord {
  id: number
  node_id: number
  is_healthy: boolean
  response_time: number | null
  error_message: string | null
  node_status: string | null
  node_version: string | null
  current_tasks: number | null
  checked_at: string
}

// MinerU 2.0 API 相关类型
export interface MinerUParseRequest {
  file_content: string
  filename: string
  parse_config?: Record<string, any>
}

export interface MinerUParseResponse {
  task_id: string
  status: string
  message?: string
}

export interface MinerUStatusResponse {
  task_id: string
  status: string
  progress?: number
  result?: Record<string, any>
  error?: string
}

export interface MinerUHealthResponse {
  status: string
  version?: string
  parse_mode?: string
  current_tasks?: number
  max_tasks?: number
}
