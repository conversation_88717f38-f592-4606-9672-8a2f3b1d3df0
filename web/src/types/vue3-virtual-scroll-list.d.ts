declare module 'vue3-virtual-scroll-list' {
  import { DefineComponent } from 'vue'
  
  interface VirtualListProps {
    dataKey: string
    dataSources: any[]
    estimateSize: number
    keeps?: number
    start?: number
    offset?: number
    direction?: 'vertical' | 'horizontal'
    topThreshold?: number
    bottomThreshold?: number
    pageMode?: boolean
    root?: HTMLElement | null
    size?: number
    buffer?: number
    scrollelement?: HTMLElement | null
  }

  const VirtualList: DefineComponent<VirtualListProps>
  export default VirtualList
} 