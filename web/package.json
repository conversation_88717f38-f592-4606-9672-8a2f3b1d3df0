{"name": "pr", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@types/markdown-it": "^14.1.2", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "antd": "^5.26.2", "axios": "^1.10.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.10.2", "markdown-it": "^14.1.0", "md-editor-v3": "^5.7.1", "path": "^0.12.7", "pinia": "^3.0.3", "react-markdown": "^10.1.0", "vue": "^3.5.13", "vue-router": "^4.5.1", "vue3-virtual-scroll-list": "^0.2.1"}, "devDependencies": {"@types/node": "^22.15.33", "@vitejs/plugin-vue": "^5.2.4", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}